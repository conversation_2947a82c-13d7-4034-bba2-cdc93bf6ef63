package com.interview.controller;

import com.interview.service.QuestionService;
import com.interview.dto.QuestionDto;
import com.interview.entity.Question;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 问题控制器测试
 */
@WebMvcTest(QuestionController.class)
public class QuestionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private QuestionService questionService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testGetCategories() throws Exception {
        // 准备测试数据
        List<String> categories = Arrays.asList("Java", "Python", "JavaScript", "算法", "数据结构", "系统设计");
        
        when(questionService.getAllCategories()).thenReturn(categories);

        // 执行测试
        mockMvc.perform(get("/questions/categories"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(6));
    }

    @Test
    public void testInitSampleQuestions() throws Exception {
        // 准备测试数据
        QuestionDto.BatchOperationResponse response = QuestionDto.BatchOperationResponse.builder()
                .successCount(6)
                .failureCount(0)
                .errors(Arrays.asList())
                .build();
        
        when(questionService.initSampleQuestions()).thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/questions/init"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.successCount").value(6))
                .andExpect(jsonPath("$.data.failureCount").value(0));
    }

    @Test
    public void testGetQuestions() throws Exception {
        // 准备测试数据
        QuestionDto.QuestionInfo questionInfo = QuestionDto.QuestionInfo.builder()
                .id(1L)
                .title("什么是Java中的多态？")
                .content("请解释Java中多态的概念")
                .category("Java")
                .difficulty(Question.Difficulty.MEDIUM)
                .type(Question.QuestionType.TECHNICAL)
                .build();

        QuestionDto.QuestionListResponse listResponse = QuestionDto.QuestionListResponse.builder()
                .questions(Arrays.asList(questionInfo))
                .totalElements(1)
                .totalPages(1)
                .currentPage(0)
                .pageSize(10)
                .hasNext(false)
                .hasPrevious(false)
                .build();
        
        when(questionService.getQuestions(any(QuestionDto.QuestionQueryRequest.class))).thenReturn(listResponse);

        // 执行测试
        mockMvc.perform(get("/questions")
                .param("category", "Java")
                .param("difficulty", "MEDIUM")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.questions").isArray())
                .andExpect(jsonPath("$.data.questions.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].title").value("什么是Java中的多态？"));
    }

    @Test
    public void testGetQuestionById() throws Exception {
        // 准备测试数据
        QuestionDto.QuestionInfo questionInfo = QuestionDto.QuestionInfo.builder()
                .id(1L)
                .title("什么是Java中的多态？")
                .content("请解释Java中多态的概念")
                .category("Java")
                .difficulty(Question.Difficulty.MEDIUM)
                .type(Question.QuestionType.TECHNICAL)
                .build();
        
        when(questionService.getQuestionById(1L)).thenReturn(Optional.of(questionInfo));

        // 执行测试
        mockMvc.perform(get("/questions/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.title").value("什么是Java中的多态？"));
    }

    @Test
    public void testCreateQuestion() throws Exception {
        // 准备测试数据
        QuestionDto.CreateQuestionRequest request = QuestionDto.CreateQuestionRequest.builder()
                .title("测试问题")
                .content("这是一个测试问题")
                .category("Java")
                .difficulty(Question.Difficulty.EASY)
                .type(Question.QuestionType.TECHNICAL)
                .timeLimit(300)
                .build();

        QuestionDto.QuestionInfo createdQuestion = QuestionDto.QuestionInfo.builder()
                .id(1L)
                .title("测试问题")
                .content("这是一个测试问题")
                .category("Java")
                .difficulty(Question.Difficulty.EASY)
                .type(Question.QuestionType.TECHNICAL)
                .timeLimit(300)
                .build();
        
        when(questionService.createQuestion(any(QuestionDto.CreateQuestionRequest.class))).thenReturn(createdQuestion);

        // 执行测试
        mockMvc.perform(post("/questions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.title").value("测试问题"));
    }

    @Test
    public void testSearchQuestions() throws Exception {
        // 准备测试数据
        QuestionDto.QuestionInfo questionInfo = QuestionDto.QuestionInfo.builder()
                .id(1L)
                .title("Java多态问题")
                .content("关于Java多态的问题")
                .category("Java")
                .difficulty(Question.Difficulty.MEDIUM)
                .type(Question.QuestionType.TECHNICAL)
                .build();

        QuestionDto.QuestionListResponse listResponse = QuestionDto.QuestionListResponse.builder()
                .questions(Arrays.asList(questionInfo))
                .totalElements(1)
                .totalPages(1)
                .currentPage(0)
                .pageSize(10)
                .hasNext(false)
                .hasPrevious(false)
                .build();
        
        when(questionService.getQuestions(any(QuestionDto.QuestionQueryRequest.class))).thenReturn(listResponse);

        // 执行测试
        mockMvc.perform(get("/questions/search")
                .param("query", "Java")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpected(jsonPath("$.data.questions").isArray());
    }
}
