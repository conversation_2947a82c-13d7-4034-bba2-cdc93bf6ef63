package com.interview.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Random;

/**
 * 短信服务
 * MVP阶段使用Mock实现，生产环境需要接入真实短信服务商
 */
@Service
@Slf4j
public class SmsService {

    @Value("${sms.provider:mock}")
    private String provider;

    @Value("${sms.mock.enabled:true}")
    private boolean mockEnabled;

    @Value("${sms.mock.default-code:123456}")
    private String defaultCode;

    private final Random random = new Random();

    /**
     * 发送验证码
     */
    public void sendCode(String phone, String code) {
        if (mockEnabled || "mock".equals(provider)) {
            sendMockCode(phone, code);
        } else {
            sendRealCode(phone, code);
        }
    }

    /**
     * 生成验证码
     */
    public String generateCode() {
        if (mockEnabled) {
            return defaultCode;
        }
        
        // 生成6位随机数字验证码
        int code = 100000 + random.nextInt(900000);
        return String.valueOf(code);
    }

    /**
     * Mock发送验证码（开发环境）
     */
    private void sendMockCode(String phone, String code) {
        log.info("=== Mock短信服务 ===");
        log.info("手机号: {}", phone);
        log.info("验证码: {}", code);
        log.info("=================");
        
        // 模拟发送延迟
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 真实短信发送（生产环境）
     */
    private void sendRealCode(String phone, String code) {
        try {
            // TODO: 接入真实短信服务商API
            // 例如：阿里云短信、腾讯云短信、华为云短信等
            
            // 示例代码结构：
            // SmsClient client = new SmsClient(accessKey, secretKey);
            // SendSmsRequest request = new SendSmsRequest()
            //     .setPhoneNumbers(phone)
            //     .setSignName("面试助手")
            //     .setTemplateCode("SMS_123456")
            //     .setTemplateParam("{\"code\":\"" + code + "\"}");
            // SendSmsResponse response = client.sendSms(request);
            
            log.info("真实短信发送成功: phone={}, code={}", phone, code);
            
        } catch (Exception e) {
            log.error("短信发送失败: phone={}, error={}", phone, e.getMessage());
            throw new RuntimeException("短信发送失败: " + e.getMessage());
        }
    }

    /**
     * 验证手机号格式
     */
    public boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        
        // 中国大陆手机号正则表达式
        String phoneRegex = "^1[3-9]\\d{9}$";
        return phone.matches(phoneRegex);
    }

    /**
     * 验证验证码格式
     */
    public boolean isValidCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        
        // 6位数字验证码
        String codeRegex = "^\\d{6}$";
        return code.matches(codeRegex);
    }

    /**
     * 格式化手机号显示
     */
    public String formatPhone(String phone) {
        if (!isValidPhone(phone)) {
            return phone;
        }
        
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 获取短信模板内容
     */
    private String getSmsTemplate(String code) {
        return String.format("【面试助手】您的验证码是%s，5分钟内有效，请勿泄露给他人。", code);
    }

    /**
     * 检查发送频率限制
     * TODO: 实现基于Redis的频率限制
     */
    public boolean checkRateLimit(String phone) {
        // 简单的内存级别限制，生产环境应该使用Redis
        // 这里返回true表示允许发送
        return true;
    }

    /**
     * 记录发送日志
     */
    private void logSmsRecord(String phone, String code, boolean success, String error) {
        // TODO: 记录到数据库或日志系统
        if (success) {
            log.info("短信发送成功记录: phone={}, code={}", formatPhone(phone), "******");
        } else {
            log.error("短信发送失败记录: phone={}, error={}", formatPhone(phone), error);
        }
    }
}
