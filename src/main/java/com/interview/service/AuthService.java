package com.interview.service;

import com.interview.dto.AuthDto;
import com.interview.dto.UserDto;
import com.interview.entity.User;
import com.interview.repository.UserRepository;
import com.interview.config.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AuthService {

    private final UserRepository userRepository;
    private final UserService userService;
    private final SmsService smsService;
    private final JwtTokenProvider jwtTokenProvider;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String SMS_CODE_PREFIX = "sms_code:";
    private static final String REFRESH_TOKEN_PREFIX = "refresh_token:";
    private static final int SMS_CODE_EXPIRE_MINUTES = 5;
    private static final int REFRESH_TOKEN_EXPIRE_DAYS = 7;

    /**
     * 发送验证码
     */
    public void sendCode(AuthDto.SendCodeRequest request) {
        String phone = request.getPhone();
        
        // 检查发送频率限制
        String codeKey = SMS_CODE_PREFIX + phone;
        if (redisTemplate.hasKey(codeKey)) {
            throw new RuntimeException("验证码已发送，请稍后再试");
        }
        
        // 生成验证码
        String code = smsService.generateCode();
        
        // 发送短信
        smsService.sendCode(phone, code);
        
        // 缓存验证码
        redisTemplate.opsForValue().set(codeKey, code, SMS_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        log.info("验证码已发送到手机号: {}", phone);
    }

    /**
     * 用户登录
     */
    public AuthDto.LoginResponse login(AuthDto.LoginRequest request) {
        String phone = request.getPhone();
        String code = request.getCode();
        
        // 验证验证码
        if (!validateCode(phone, code)) {
            throw new RuntimeException("验证码错误或已过期");
        }
        
        // 查找或创建用户
        User user = userRepository.findByPhone(phone)
                .orElseGet(() -> createNewUser(phone));
        
        // 生成Token
        String token = jwtTokenProvider.generateToken(user.getId().toString());
        String refreshToken = jwtTokenProvider.generateRefreshToken(user.getId().toString());
        
        // 缓存刷新Token
        String refreshKey = REFRESH_TOKEN_PREFIX + user.getId();
        redisTemplate.opsForValue().set(refreshKey, refreshToken, REFRESH_TOKEN_EXPIRE_DAYS, TimeUnit.DAYS);
        
        // 清除验证码
        redisTemplate.delete(SMS_CODE_PREFIX + phone);
        
        // 构建响应
        UserDto.UserInfo userInfo = buildUserInfo(user);
        
        log.info("用户登录成功: {}", phone);
        
        return AuthDto.LoginResponse.builder()
                .user(userInfo)
                .token(token)
                .refreshToken(refreshToken)
                .expiresIn(jwtTokenProvider.getExpirationTime())
                .build();
    }

    /**
     * 密码登录
     */
    public AuthDto.LoginResponse loginWithPassword(String phone, String password) {
        // 查找用户
        User user = userRepository.findByPhone(phone)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 验证密码
        if (!userService.validatePassword(user, password)) {
            throw new RuntimeException("密码错误");
        }

        // 生成Token
        String token = jwtTokenProvider.generateToken(user.getId().toString());
        String refreshToken = jwtTokenProvider.generateRefreshToken(user.getId().toString());

        // 缓存刷新Token
        String refreshKey = REFRESH_TOKEN_PREFIX + user.getId();
        redisTemplate.opsForValue().set(refreshKey, refreshToken, REFRESH_TOKEN_EXPIRE_DAYS, TimeUnit.DAYS);

        // 构建响应
        UserDto.UserInfo userInfo = buildUserInfo(user);

        log.info("用户密码登录成功: {}", phone);

        return AuthDto.LoginResponse.builder()
                .user(userInfo)
                .token(token)
                .refreshToken(refreshToken)
                .expiresIn(jwtTokenProvider.getExpirationTime())
                .build();
    }

    /**
     * 刷新Token
     */
    public AuthDto.RefreshTokenResponse refreshToken(AuthDto.RefreshTokenRequest request) {
        String refreshToken = request.getRefreshToken();
        
        // 验证刷新Token
        if (!jwtTokenProvider.validateToken(refreshToken)) {
            throw new RuntimeException("刷新Token无效");
        }
        
        // 获取用户ID
        String userId = jwtTokenProvider.getUserIdFromToken(refreshToken);
        
        // 检查Redis中的刷新Token
        String refreshKey = REFRESH_TOKEN_PREFIX + userId;
        String cachedRefreshToken = (String) redisTemplate.opsForValue().get(refreshKey);
        
        if (!refreshToken.equals(cachedRefreshToken)) {
            throw new RuntimeException("刷新Token已失效");
        }
        
        // 生成新的Token
        String newToken = jwtTokenProvider.generateToken(userId);
        String newRefreshToken = jwtTokenProvider.generateRefreshToken(userId);
        
        // 更新缓存
        redisTemplate.opsForValue().set(refreshKey, newRefreshToken, REFRESH_TOKEN_EXPIRE_DAYS, TimeUnit.DAYS);
        
        log.info("Token刷新成功: userId={}", userId);
        
        return AuthDto.RefreshTokenResponse.builder()
                .token(newToken)
                .refreshToken(newRefreshToken)
                .expiresIn(jwtTokenProvider.getExpirationTime())
                .build();
    }

    /**
     * 用户登出
     */
    public void logout(AuthDto.LogoutRequest request) {
        String token = request.getToken();
        
        if (token != null && jwtTokenProvider.validateToken(token)) {
            String userId = jwtTokenProvider.getUserIdFromToken(token);
            
            // 删除刷新Token
            String refreshKey = REFRESH_TOKEN_PREFIX + userId;
            redisTemplate.delete(refreshKey);
            
            // 将Token加入黑名单
            String tokenKey = "blacklist_token:" + token;
            long expiration = jwtTokenProvider.getExpirationFromToken(token).getTime() - System.currentTimeMillis();
            if (expiration > 0) {
                redisTemplate.opsForValue().set(tokenKey, "true", expiration, TimeUnit.MILLISECONDS);
            }
            
            log.info("用户登出成功: userId={}", userId);
        }
    }

    /**
     * 验证验证码
     */
    private boolean validateCode(String phone, String code) {
        String codeKey = SMS_CODE_PREFIX + phone;
        String cachedCode = (String) redisTemplate.opsForValue().get(codeKey);
        return code.equals(cachedCode);
    }

    /**
     * 创建新用户
     */
    private User createNewUser(String phone) {
        User user = User.builder()
                .phone(phone)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        
        return userRepository.save(user);
    }

    /**
     * 构建用户信息DTO
     */
    private UserDto.UserInfo buildUserInfo(User user) {
        return UserDto.UserInfo.builder()
                .id(user.getId())
                .phone(user.getPhone())
                .nickname(user.getNickname())
                .experience(user.getExperience())
                .position(user.getPosition())
                .skills(user.getSkills())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .profileComplete(user.isProfileComplete())
                .build();
    }

    /**
     * 检查Token是否在黑名单中
     */
    public boolean isTokenBlacklisted(String token) {
        String tokenKey = "blacklist_token:" + token;
        return redisTemplate.hasKey(tokenKey);
    }

    /**
     * 修改密码
     */
    public void changePassword(Long userId, AuthDto.ChangePasswordRequest request) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // TODO: 验证旧密码（当前系统使用验证码登录，暂时跳过密码验证）
        // 在实际应用中，这里应该验证旧密码是否正确

        // TODO: 加密并保存新密码
        // 当前系统使用验证码登录，暂时不存储密码
        // 在实际应用中，这里应该使用BCrypt等方式加密密码并保存

        log.info("用户密码修改成功: userId={}", userId);
    }

    /**
     * 忘记密码
     */
    public void forgotPassword(AuthDto.ForgotPasswordRequest request) {
        String phone = request.getPhone();

        // 检查用户是否存在
        if (!userRepository.existsByPhone(phone)) {
            throw new RuntimeException("手机号未注册");
        }

        // 检查发送频率限制
        String codeKey = "reset_code:" + phone;
        if (redisTemplate.hasKey(codeKey)) {
            throw new RuntimeException("重置密码验证码已发送，请稍后再试");
        }

        // 生成验证码
        String code = smsService.generateCode();

        // 发送短信
        smsService.sendCode(phone, code);

        // 缓存验证码（重置密码验证码有效期较短）
        redisTemplate.opsForValue().set(codeKey, code, 10, TimeUnit.MINUTES);

        log.info("重置密码验证码已发送到手机号: {}", phone);
    }

    /**
     * 重置密码
     */
    public void resetPassword(AuthDto.ResetPasswordRequest request) {
        String phone = request.getPhone();
        String code = request.getCode();

        // 验证验证码
        String codeKey = "reset_code:" + phone;
        String cachedCode = (String) redisTemplate.opsForValue().get(codeKey);
        if (!code.equals(cachedCode)) {
            throw new RuntimeException("验证码错误或已过期");
        }

        // 查找用户
        User user = userRepository.findByPhone(phone)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // TODO: 加密并保存新密码
        // 当前系统使用验证码登录，暂时不存储密码
        // 在实际应用中，这里应该使用BCrypt等方式加密密码并保存

        // 清除验证码
        redisTemplate.delete(codeKey);

        log.info("用户密码重置成功: phone={}", phone);
    }
}
