package com.interview.service;

import com.interview.entity.Message;
import com.interview.entity.User;
import com.interview.dto.InterviewDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 大模型服务 - 集成百度文心一言API
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LLMService {

    @Value("${llm.baidu.api-key}")
    private String apiKey;

    @Value("${llm.baidu.secret-key}")
    private String secretKey;

    @Value("${llm.baidu.url}")
    private String apiUrl;

    @Value("${llm.baidu.model:ERNIE-Bot-turbo}")
    private String model;

    private final RestTemplate restTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;

    private static final String ACCESS_TOKEN_KEY = "baidu_access_token";
    private static final String CACHE_PREFIX = "llm_cache:";

    /**
     * 生成面试开场问题
     */
    public String generateInitialQuestion(User user) {
        String systemPrompt = buildSystemPrompt(user);
        String userPrompt = "请开始面试，先进行简单的自我介绍环节。";
        
        return generateResponse(systemPrompt, userPrompt, Collections.emptyList());
    }

    /**
     * 生成面试回复
     */
    public String generateResponse(String userMessage, List<Message> context, User user) {
        String systemPrompt = buildSystemPrompt(user);
        return generateResponse(systemPrompt, userMessage, context);
    }

    /**
     * 生成面试评估
     */
    public InterviewDto.InterviewResult generateEvaluation(List<Message> messages, User user) {
        try {
            String evaluationPrompt = buildEvaluationPrompt(messages, user);
            String response = generateResponse("你是一个专业的面试评估专家。", evaluationPrompt, Collections.emptyList());
            
            return parseEvaluationResponse(response);
            
        } catch (Exception e) {
            log.error("生成面试评估失败: {}", e.getMessage());
            return createDefaultEvaluation();
        }
    }

    /**
     * 核心生成方法
     */
    private String generateResponse(String systemPrompt, String userMessage, List<Message> context) {
        try {
            // 检查缓存
            String cacheKey = CACHE_PREFIX + generateCacheKey(systemPrompt, userMessage, context);
            String cachedResponse = (String) redisTemplate.opsForValue().get(cacheKey);
            if (cachedResponse != null) {
                log.debug("使用缓存的LLM响应");
                return cachedResponse;
            }

            // 构建请求
            Map<String, Object> request = buildLLMRequest(systemPrompt, userMessage, context);
            
            // 获取访问令牌
            String accessToken = getAccessToken();
            
            // 调用API
            String response = callBaiduAPI(request, accessToken);
            
            // 缓存响应
            redisTemplate.opsForValue().set(cacheKey, response, 1, TimeUnit.HOURS);
            
            return response;
            
        } catch (Exception e) {
            log.error("调用大模型API失败: {}", e.getMessage());
            return generateFallbackResponse(userMessage);
        }
    }

    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(User user) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是一个专业的面试官，正在对一位求职者进行面试。\n\n");
        
        if (user != null) {
            prompt.append("候选人信息：\n");
            if (user.getNickname() != null) {
                prompt.append("- 姓名：").append(user.getNickname()).append("\n");
            }
            if (user.getPosition() != null) {
                prompt.append("- 应聘职位：").append(user.getPosition()).append("\n");
            }
            if (user.getExperience() != null) {
                prompt.append("- 工作经验：").append(user.getExperience()).append("\n");
            }
            if (user.getSkills() != null && !user.getSkills().isEmpty()) {
                prompt.append("- 技能：").append(String.join(", ", user.getSkills())).append("\n");
            }
        }
        
        prompt.append("\n面试要求：\n");
        prompt.append("1. 保持专业、友好的态度\n");
        prompt.append("2. 问题要有针对性和深度\n");
        prompt.append("3. 根据候选人的回答进行后续提问\n");
        prompt.append("4. 每次只问一个问题\n");
        prompt.append("5. 问题长度控制在100字以内\n");
        prompt.append("6. 避免过于复杂的技术细节\n");
        
        return prompt.toString();
    }

    /**
     * 构建LLM请求
     */
    private Map<String, Object> buildLLMRequest(String systemPrompt, String userMessage, List<Message> context) {
        List<Map<String, String>> messages = new ArrayList<>();
        
        // 添加系统提示
        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", systemPrompt);
        messages.add(systemMessage);

        // 添加历史对话（最近10条）
        int contextSize = Math.min(context.size(), 10);
        for (int i = context.size() - contextSize; i < context.size(); i++) {
            Message msg = context.get(i);
            String role = msg.getRole() == Message.MessageRole.USER ? "user" : "assistant";
            Map<String, String> contextMessage = new HashMap<>();
            contextMessage.put("role", role);
            contextMessage.put("content", msg.getContent());
            messages.add(contextMessage);
        }

        // 添加当前用户消息
        Map<String, String> userMessage2 = new HashMap<>();
        userMessage2.put("role", "user");
        userMessage2.put("content", userMessage);
        messages.add(userMessage2);
        
        Map<String, Object> request = new HashMap<>();
        request.put("messages", messages);
        request.put("temperature", 0.7);
        request.put("max_output_tokens", 500);
        request.put("top_p", 0.9);
        
        return request;
    }

    /**
     * 调用百度API
     */
    private String callBaiduAPI(Map<String, Object> request, String accessToken) throws Exception {
        String url = apiUrl + "?access_token=" + accessToken;
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
        
        long startTime = System.currentTimeMillis();
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
        long responseTime = System.currentTimeMillis() - startTime;
        
        log.info("百度API调用完成，耗时: {}ms", responseTime);
        
        if (response.getStatusCode() == HttpStatus.OK) {
            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            if (jsonNode.has("result")) {
                return jsonNode.get("result").asText();
            } else if (jsonNode.has("error_msg")) {
                throw new RuntimeException("百度API错误: " + jsonNode.get("error_msg").asText());
            }
        }
        
        throw new RuntimeException("百度API调用失败: " + response.getStatusCode());
    }

    /**
     * 获取百度访问令牌
     */
    private String getAccessToken() {
        String cachedToken = (String) redisTemplate.opsForValue().get(ACCESS_TOKEN_KEY);
        if (cachedToken != null) {
            return cachedToken;
        }
        
        try {
            String tokenUrl = "https://aip.baidubce.com/oauth/2.0/token";
            String params = String.format("grant_type=client_credentials&client_id=%s&client_secret=%s", 
                    apiKey, secretKey);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            HttpEntity<String> entity = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(tokenUrl, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                String accessToken = jsonNode.get("access_token").asText();
                int expiresIn = jsonNode.get("expires_in").asInt();
                
                // 缓存token，提前5分钟过期
                redisTemplate.opsForValue().set(ACCESS_TOKEN_KEY, accessToken, 
                        expiresIn - 300, TimeUnit.SECONDS);
                
                return accessToken;
            }
            
            throw new RuntimeException("获取访问令牌失败: " + response.getStatusCode());
            
        } catch (Exception e) {
            log.error("获取百度访问令牌失败: {}", e.getMessage());
            throw new RuntimeException("获取访问令牌失败", e);
        }
    }

    /**
     * 构建评估提示词
     */
    private String buildEvaluationPrompt(List<Message> messages, User user) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请对以下面试对话进行专业评估，并以JSON格式返回结果：\n\n");
        
        // 添加对话内容
        for (Message message : messages) {
            String role = message.getRole() == Message.MessageRole.USER ? "候选人" : "面试官";
            prompt.append(role).append(": ").append(message.getContent()).append("\n");
        }
        
        prompt.append("\n请按以下JSON格式返回评估结果：\n");
        prompt.append("{\n");
        prompt.append("  \"overallScore\": 85,\n");
        prompt.append("  \"technicalScore\": 80,\n");
        prompt.append("  \"communicationScore\": 90,\n");
        prompt.append("  \"problemSolvingScore\": 85,\n");
        prompt.append("  \"adaptabilityScore\": 80,\n");
        prompt.append("  \"strengths\": [\"表达清晰\", \"技术基础扎实\"],\n");
        prompt.append("  \"improvements\": [\"需要加强算法基础\"],\n");
        prompt.append("  \"feedback\": \"详细的面试反馈...\"\n");
        prompt.append("}\n");
        
        return prompt.toString();
    }

    /**
     * 解析评估响应
     */
    private InterviewDto.InterviewResult parseEvaluationResponse(String response) {
        try {
            // 尝试从响应中提取JSON
            String jsonStr = extractJsonFromResponse(response);
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            
            return InterviewDto.InterviewResult.builder()
                    .overallScore(jsonNode.path("overallScore").asInt(75))
                    .scoreBreakdown(InterviewDto.ScoreBreakdown.builder()
                            .technicalScore(jsonNode.path("technicalScore").asInt(75))
                            .communicationScore(jsonNode.path("communicationScore").asInt(75))
                            .problemSolvingScore(jsonNode.path("problemSolvingScore").asInt(75))
                            .adaptabilityScore(jsonNode.path("adaptabilityScore").asInt(75))
                            .build())
                    .strengths(parseStringArray(jsonNode.path("strengths")))
                    .improvements(parseStringArray(jsonNode.path("improvements")))
                    .feedback(jsonNode.path("feedback").asText("面试表现良好，继续保持。"))
                    .build();
                    
        } catch (Exception e) {
            log.error("解析评估响应失败: {}", e.getMessage());
            return createDefaultEvaluation();
        }
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String systemPrompt, String userMessage, List<Message> context) {
        String combined = systemPrompt + userMessage + context.size();
        return String.valueOf(combined.hashCode());
    }

    /**
     * 生成降级响应
     */
    private String generateFallbackResponse(String userMessage) {
        String[] fallbackResponses = {
            "很好，请继续详细说明一下。",
            "这个回答不错，能举个具体的例子吗？",
            "我理解了，那你在这个过程中遇到了什么挑战？",
            "听起来很有趣，你是如何解决这个问题的？",
            "好的，让我们换个话题，你对我们公司有什么了解？"
        };
        
        return fallbackResponses[new Random().nextInt(fallbackResponses.length)];
    }

    /**
     * 创建默认评估
     */
    private InterviewDto.InterviewResult createDefaultEvaluation() {
        return InterviewDto.InterviewResult.builder()
                .overallScore(75)
                .scoreBreakdown(InterviewDto.ScoreBreakdown.builder()
                        .technicalScore(75)
                        .communicationScore(75)
                        .problemSolvingScore(75)
                        .adaptabilityScore(75)
                        .build())
                .strengths(Arrays.asList("表达清晰", "态度积极"))
                .improvements(Arrays.asList("可以更加深入地阐述技术细节"))
                .feedback("整体表现良好，建议在技术深度方面继续提升。")
                .build();
    }

    /**
     * 从响应中提取JSON
     */
    private String extractJsonFromResponse(String response) {
        int start = response.indexOf('{');
        int end = response.lastIndexOf('}');
        if (start >= 0 && end > start) {
            return response.substring(start, end + 1);
        }
        return "{}";
    }

    /**
     * 解析字符串数组
     */
    private List<String> parseStringArray(JsonNode arrayNode) {
        List<String> result = new ArrayList<>();
        if (arrayNode.isArray()) {
            arrayNode.forEach(node -> result.add(node.asText()));
        }
        return result;
    }
}
