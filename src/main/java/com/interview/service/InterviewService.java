package com.interview.service;

import com.interview.dto.InterviewDto;
import com.interview.entity.InterviewSession;
import com.interview.entity.Message;
import com.interview.entity.User;
import com.interview.repository.InterviewSessionRepository;
import com.interview.repository.MessageRepository;
import com.interview.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 面试服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class InterviewService {

    private final InterviewSessionRepository sessionRepository;
    private final MessageRepository messageRepository;
    private final UserRepository userRepository;
    private final LLMService llmService;

    /**
     * 开始面试
     */
    public InterviewDto.StartInterviewResponse startInterview(Long userId, InterviewDto.StartInterviewRequest request) {
        // 检查用户是否存在
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 检查是否有活跃的面试会话
        Optional<InterviewSession> activeSession = sessionRepository.findActiveSessionByUserId(userId);
        if (activeSession.isPresent()) {
            throw new RuntimeException("您已有一个进行中的面试，请先完成或放弃当前面试");
        }

        // 创建新的面试会话
        String sessionId = UUID.randomUUID().toString();
        InterviewSession session = InterviewSession.builder()
                .id(sessionId)
                .user(user)
                .status(InterviewSession.SessionStatus.ACTIVE)
                .startTime(LocalDateTime.now())
                .build();

        session = sessionRepository.save(session);

        // 生成开场问题
        String initialQuestion = llmService.generateInitialQuestion(user);

        // 保存开场消息
        Message initialMessage = Message.createAssistantMessage(session, initialQuestion);
        initialMessage = messageRepository.save(initialMessage);

        log.info("面试开始: userId={}, sessionId={}", userId, sessionId);

        return InterviewDto.StartInterviewResponse.builder()
                .sessionId(sessionId)
                .initialMessage(convertToMessageInfo(initialMessage))
                .interviewType(request.getInterviewType())
                .startTime(session.getStartTime())
                .build();
    }

    /**
     * 发送消息
     */
    public InterviewDto.SendMessageResponse sendMessage(String sessionId, String userMessage, Long userId) {
        // 验证会话
        InterviewSession session = sessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("面试会话不存在"));

        if (!session.getUser().getId().equals(userId)) {
            throw new RuntimeException("无权访问此面试会话");
        }

        if (!session.isActive()) {
            throw new RuntimeException("面试会话已结束");
        }

        // 保存用户消息
        Message userMsg = Message.createUserMessage(session, userMessage);
        userMsg = messageRepository.save(userMsg);

        // 获取历史对话
        List<Message> context = messageRepository.findBySessionOrderByCreatedAtAsc(session);

        // 生成AI回复
        long startTime = System.currentTimeMillis();
        String aiResponse = llmService.generateResponse(userMessage, context, session.getUser());
        long responseTime = System.currentTimeMillis() - startTime;

        // 保存AI回复
        Message aiMsg = Message.createAssistantMessage(session, aiResponse, null, responseTime);
        aiMsg = messageRepository.save(aiMsg);

        // 更新会话时间
        session.setUpdatedAt(LocalDateTime.now());
        sessionRepository.save(session);

        log.info("消息发送成功: sessionId={}, responseTime={}ms", sessionId, responseTime);

        return InterviewDto.SendMessageResponse.builder()
                .message(convertToMessageInfo(aiMsg))
                .remainingQuestions(calculateRemainingQuestions(context.size()))
                .build();
    }

    /**
     * 结束面试
     */
    public InterviewDto.InterviewResult endInterview(String sessionId, Long userId) {
        // 验证会话
        InterviewSession session = sessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("面试会话不存在"));

        if (!session.getUser().getId().equals(userId)) {
            throw new RuntimeException("无权访问此面试会话");
        }

        if (!session.isActive()) {
            throw new RuntimeException("面试会话已结束");
        }

        // 获取所有消息
        List<Message> messages = messageRepository.findBySessionOrderByCreatedAtAsc(session);

        // 生成评估结果
        InterviewDto.InterviewResult evaluation = llmService.generateEvaluation(messages, session.getUser());

        // 更新会话状态
        session.complete();
        session.setOverallScore(evaluation.getOverallScore());
        session.setFeedback(evaluation.getFeedback());
        
        if (evaluation.getStrengths() != null) {
            session.getStrengths().addAll(evaluation.getStrengths());
        }
        if (evaluation.getImprovements() != null) {
            session.getImprovements().addAll(evaluation.getImprovements());
        }

        sessionRepository.save(session);

        // 构建完整结果
        InterviewDto.InterviewResult result = InterviewDto.InterviewResult.builder()
                .sessionId(sessionId)
                .duration(session.getDurationInSeconds())
                .messageCount(messages.size())
                .overallScore(evaluation.getOverallScore())
                .strengths(evaluation.getStrengths())
                .improvements(evaluation.getImprovements())
                .feedback(evaluation.getFeedback())
                .scoreBreakdown(evaluation.getScoreBreakdown())
                .completedAt(session.getEndTime())
                .build();

        log.info("面试结束: sessionId={}, score={}, duration={}s", 
                sessionId, result.getOverallScore(), result.getDuration());

        return result;
    }

    /**
     * 获取面试历史
     */
    public InterviewDto.InterviewHistoryResponse getInterviewHistory(Long userId, InterviewDto.InterviewHistoryRequest request) {
        // 构建分页和排序
        Sort sort = Sort.by(Sort.Direction.fromString(request.getSortDir()), request.getSortBy());
        Pageable pageable = PageRequest.of(request.getPage(), request.getSize(), sort);

        // 查询用户的面试会话
        List<InterviewSession> sessions = sessionRepository.findByUserIdOrderByCreatedAtDesc(userId);

        // 过滤状态
        if (request.getStatus() != null) {
            InterviewSession.SessionStatus status = InterviewSession.SessionStatus.valueOf(request.getStatus());
            sessions = sessions.stream()
                    .filter(s -> s.getStatus() == status)
                    .collect(Collectors.toList());
        }

        // 手动分页
        int start = request.getPage() * request.getSize();
        int end = Math.min(start + request.getSize(), sessions.size());
        List<InterviewSession> pagedSessions = sessions.subList(start, end);

        // 转换为DTO
        List<InterviewDto.SessionInfo> sessionInfos = pagedSessions.stream()
                .map(this::convertToSessionInfo)
                .collect(Collectors.toList());

        return InterviewDto.InterviewHistoryResponse.builder()
                .sessions(sessionInfos)
                .totalElements(sessions.size())
                .totalPages((int) Math.ceil((double) sessions.size() / request.getSize()))
                .currentPage(request.getPage())
                .pageSize(request.getSize())
                .build();
    }

    /**
     * 获取面试统计
     */
    public InterviewDto.InterviewStats getInterviewStats(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        List<InterviewSession> allSessions = sessionRepository.findByUserOrderByCreatedAtDesc(user);
        List<InterviewSession> completedSessions = allSessions.stream()
                .filter(s -> s.getStatus() == InterviewSession.SessionStatus.COMPLETED)
                .collect(Collectors.toList());

        // 计算统计数据
        long totalInterviews = allSessions.size();
        long completedInterviews = completedSessions.size();
        
        double averageScore = completedSessions.stream()
                .filter(s -> s.getOverallScore() != null)
                .mapToInt(InterviewSession::getOverallScore)
                .average()
                .orElse(0.0);

        int bestScore = completedSessions.stream()
                .filter(s -> s.getOverallScore() != null)
                .mapToInt(InterviewSession::getOverallScore)
                .max()
                .orElse(0);

        long totalDuration = completedSessions.stream()
                .mapToLong(InterviewSession::getDurationInSeconds)
                .sum();

        double averageDuration = completedSessions.isEmpty() ? 0.0 : 
                (double) totalDuration / completedSessions.size();

        long thisMonthInterviews = sessionRepository.countUserSessionsThisMonth(user);

        return InterviewDto.InterviewStats.builder()
                .totalInterviews(totalInterviews)
                .completedInterviews(completedInterviews)
                .averageScore(averageScore)
                .bestScore(bestScore)
                .totalDuration(totalDuration)
                .averageDuration(averageDuration)
                .thisMonthInterviews(thisMonthInterviews)
                .build();
    }

    /**
     * 放弃面试
     */
    public void abandonInterview(String sessionId, Long userId) {
        InterviewSession session = sessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("面试会话不存在"));

        if (!session.getUser().getId().equals(userId)) {
            throw new RuntimeException("无权访问此面试会话");
        }

        if (!session.isActive()) {
            throw new RuntimeException("面试会话已结束");
        }

        session.abandon();
        sessionRepository.save(session);

        log.info("面试已放弃: sessionId={}", sessionId);
    }

    /**
     * 获取会话详情
     */
    public InterviewDto.SessionInfo getSessionInfo(String sessionId, Long userId) {
        InterviewSession session = sessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("面试会话不存在"));

        if (!session.getUser().getId().equals(userId)) {
            throw new RuntimeException("无权访问此面试会话");
        }

        return convertToSessionInfo(session);
    }

    /**
     * 转换为消息信息DTO
     */
    private InterviewDto.MessageInfo convertToMessageInfo(Message message) {
        return InterviewDto.MessageInfo.builder()
                .id(message.getId())
                .sessionId(message.getSession().getId())
                .role(message.getRole().name().toLowerCase())
                .content(message.getContent())
                .timestamp(message.getCreatedAt())
                .tokenCount(message.getTokenCount())
                .responseTime(message.getResponseTime())
                .build();
    }

    /**
     * 转换为会话信息DTO
     */
    private InterviewDto.SessionInfo convertToSessionInfo(InterviewSession session) {
        return InterviewDto.SessionInfo.builder()
                .id(session.getId())
                .status(session.getStatus().name())
                .interviewType("basic") // 暂时固定
                .startTime(session.getStartTime())
                .endTime(session.getEndTime())
                .duration(session.getDurationInSeconds())
                .messageCount(session.getMessageCount())
                .overallScore(session.getOverallScore())
                .build();
    }

    /**
     * 计算剩余问题数
     */
    private Integer calculateRemainingQuestions(int currentMessageCount) {
        // 假设一次面试大约10-15个问题
        int maxQuestions = 15;
        int questionCount = currentMessageCount / 2; // 每两条消息算一个问答
        return Math.max(0, maxQuestions - questionCount);
    }
}
