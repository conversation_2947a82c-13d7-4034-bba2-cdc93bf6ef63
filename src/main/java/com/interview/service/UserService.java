package com.interview.service;

import com.interview.dto.UserDto;
import com.interview.entity.User;
import com.interview.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 用户服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * 创建用户
     */
    public UserDto.UserInfo createUser(UserDto.CreateUserRequest request) {
        // 检查手机号是否已存在
        if (userRepository.existsByPhone(request.getPhone())) {
            throw new RuntimeException("手机号已被注册");
        }

        User user = User.builder()
                .phone(request.getPhone())
                .nickname(request.getName())
                .password(passwordEncoder.encode(request.getPassword()))
                .build();

        user = userRepository.save(user);

        log.info("用户创建成功: phone={}, id={}", request.getPhone(), user.getId());

        return convertToUserInfo(user);
    }

    /**
     * 根据ID获取用户信息
     */
    public Optional<UserDto.UserInfo> getUserById(Long userId) {
        return userRepository.findById(userId)
                .map(this::convertToUserInfo);
    }

    /**
     * 根据手机号获取用户信息
     */
    public Optional<UserDto.UserInfo> getUserByPhone(String phone) {
        return userRepository.findByPhone(phone)
                .map(this::convertToUserInfo);
    }

    /**
     * 更新用户信息
     */
    public UserDto.UserInfo updateProfile(Long userId, UserDto.UpdateProfileRequest request) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 更新用户信息
        user.setNickname(request.getNickname());
        user.setExperience(request.getExperience());
        user.setPosition(request.getPosition());
        user.setSkills(request.getSkills());
        user.setUpdatedAt(LocalDateTime.now());

        User savedUser = userRepository.save(user);
        
        log.info("用户信息更新成功: userId={}, nickname={}", userId, request.getNickname());
        
        return convertToUserInfo(savedUser);
    }

    /**
     * 获取用户统计信息
     */
    public UserDto.UserStats getUserStats(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // TODO: 实现用户统计信息计算
        // 这里需要与面试服务集成，计算用户的面试统计数据
        
        return UserDto.UserStats.builder()
                .totalInterviews(0L)
                .completedInterviews(0L)
                .averageScore(0.0)
                .bestScore(0)
                .totalMessages(0L)
                .thisMonthInterviews(0L)
                .build();
    }

    /**
     * 检查用户是否存在
     */
    public boolean existsByPhone(String phone) {
        return userRepository.existsByPhone(phone);
    }

    /**
     * 检查用户信息是否完整
     */
    public boolean isProfileComplete(Long userId) {
        return userRepository.findById(userId)
                .map(User::isProfileComplete)
                .orElse(false);
    }



    /**
     * 验证用户密码
     */
    public boolean validatePassword(User user, String rawPassword) {
        if (user.getPassword() == null || rawPassword == null) {
            return false;
        }
        return passwordEncoder.matches(rawPassword, user.getPassword());
    }

    /**
     * 转换为用户信息DTO
     */
    private UserDto.UserInfo convertToUserInfo(User user) {
        return UserDto.UserInfo.builder()
                .id(user.getId())
                .phone(user.getPhone())
                .nickname(user.getNickname())
                .experience(user.getExperience())
                .position(user.getPosition())
                .skills(user.getSkills())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .profileComplete(user.isProfileComplete())
                .build();
    }
}
