package com.interview.service;

import com.interview.dto.QuestionDto;
import com.interview.entity.Question;
import com.interview.repository.QuestionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 问题服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class QuestionService {

    private final QuestionRepository questionRepository;

    /**
     * 创建问题
     */
    public QuestionDto.QuestionInfo createQuestion(QuestionDto.CreateQuestionRequest request) {
        Question question = Question.builder()
                .title(request.getTitle())
                .content(request.getContent())
                .category(request.getCategory())
                .difficulty(request.getDifficulty())
                .type(request.getType())
                .tags(request.getTags() != null ? new ArrayList<>(request.getTags()) : new ArrayList<>())
                .expectedAnswer(request.getExpectedAnswer())
                .scoringCriteria(request.getScoringCriteria())
                .timeLimit(request.getTimeLimit())
                .isActive(true)
                .usageCount(0L)
                .build();

        question = questionRepository.save(question);
        
        log.info("问题创建成功: id={}, title={}", question.getId(), question.getTitle());
        
        return convertToQuestionInfo(question);
    }

    /**
     * 根据ID获取问题
     */
    @Transactional(readOnly = true)
    public Optional<QuestionDto.QuestionInfo> getQuestionById(Long id) {
        return questionRepository.findById(id)
                .filter(Question::getIsActive)
                .map(this::convertToQuestionInfo);
    }

    /**
     * 更新问题
     */
    public Optional<QuestionDto.QuestionInfo> updateQuestion(Long id, QuestionDto.UpdateQuestionRequest request) {
        return questionRepository.findById(id)
                .filter(Question::getIsActive)
                .map(question -> {
                    if (request.getTitle() != null) {
                        question.setTitle(request.getTitle());
                    }
                    if (request.getContent() != null) {
                        question.setContent(request.getContent());
                    }
                    if (request.getCategory() != null) {
                        question.setCategory(request.getCategory());
                    }
                    if (request.getDifficulty() != null) {
                        question.setDifficulty(request.getDifficulty());
                    }
                    if (request.getType() != null) {
                        question.setType(request.getType());
                    }
                    if (request.getTags() != null) {
                        question.setTags(new ArrayList<>(request.getTags()));
                    }
                    if (request.getExpectedAnswer() != null) {
                        question.setExpectedAnswer(request.getExpectedAnswer());
                    }
                    if (request.getScoringCriteria() != null) {
                        question.setScoringCriteria(request.getScoringCriteria());
                    }
                    if (request.getTimeLimit() != null) {
                        question.setTimeLimit(request.getTimeLimit());
                    }
                    if (request.getIsActive() != null) {
                        question.setIsActive(request.getIsActive());
                    }

                    question = questionRepository.save(question);
                    
                    log.info("问题更新成功: id={}", id);
                    
                    return convertToQuestionInfo(question);
                });
    }

    /**
     * 删除问题（软删除）
     */
    public boolean deleteQuestion(Long id) {
        return questionRepository.findById(id)
                .filter(Question::getIsActive)
                .map(question -> {
                    question.setIsActive(false);
                    questionRepository.save(question);
                    
                    log.info("问题删除成功: id={}", id);
                    
                    return true;
                })
                .orElse(false);
    }

    /**
     * 分页查询问题
     */
    @Transactional(readOnly = true)
    public QuestionDto.QuestionListResponse getQuestions(QuestionDto.QuestionQueryRequest request) {
        // 构建排序
        Sort sort = Sort.by(
                "desc".equalsIgnoreCase(request.getSortDirection()) ? Sort.Direction.DESC : Sort.Direction.ASC,
                request.getSortBy()
        );
        
        Pageable pageable = PageRequest.of(request.getPage(), request.getSize(), sort);
        
        Page<Question> questionPage;
        
        // 根据查询条件选择查询方法
        if (request.getQuery() != null && !request.getQuery().trim().isEmpty()) {
            // 搜索查询
            questionPage = questionRepository.searchQuestions(request.getQuery().trim(), pageable);
        } else {
            // 条件查询
            questionPage = questionRepository.findByConditions(
                    request.getCategory(),
                    request.getDifficulty(),
                    request.getType(),
                    pageable
            );
        }
        
        List<QuestionDto.QuestionInfo> questions = questionPage.getContent().stream()
                .map(this::convertToQuestionInfo)
                .collect(Collectors.toList());
        
        return QuestionDto.QuestionListResponse.builder()
                .questions(questions)
                .totalElements((int) questionPage.getTotalElements())
                .totalPages(questionPage.getTotalPages())
                .currentPage(questionPage.getNumber())
                .pageSize(questionPage.getSize())
                .hasNext(questionPage.hasNext())
                .hasPrevious(questionPage.hasPrevious())
                .build();
    }

    /**
     * 获取所有分类
     */
    @Transactional(readOnly = true)
    public List<String> getAllCategories() {
        return questionRepository.findAllCategories();
    }

    /**
     * 获取所有标签
     */
    @Transactional(readOnly = true)
    public List<String> getAllTags() {
        return questionRepository.findAllTags();
    }

    /**
     * 随机获取问题
     */
    @Transactional(readOnly = true)
    public List<QuestionDto.QuestionInfo> getRandomQuestions(QuestionDto.RandomQuestionRequest request) {
        List<Question> questions;
        
        if (request.getCategory() != null || request.getDifficulty() != null || request.getType() != null) {
            // 根据条件随机获取
            String difficulty = request.getDifficulty() != null ? request.getDifficulty().name() : null;
            String type = request.getType() != null ? request.getType().name() : null;
            
            questions = questionRepository.findRandomQuestionsByConditions(
                    request.getCategory(),
                    difficulty,
                    type,
                    request.getCount()
            );
        } else {
            // 完全随机获取
            questions = questionRepository.findRandomQuestions(request.getCount());
        }
        
        // 排除指定的问题ID
        if (request.getExcludeIds() != null && !request.getExcludeIds().isEmpty()) {
            questions = questions.stream()
                    .filter(q -> !request.getExcludeIds().contains(q.getId()))
                    .collect(Collectors.toList());
        }
        
        return questions.stream()
                .map(this::convertToQuestionInfo)
                .collect(Collectors.toList());
    }

    /**
     * 增加问题使用次数
     */
    public void incrementUsageCount(Long questionId) {
        questionRepository.findById(questionId)
                .filter(Question::getIsActive)
                .ifPresent(question -> {
                    question.incrementUsageCount();
                    questionRepository.save(question);
                    
                    log.debug("问题使用次数增加: id={}, count={}", questionId, question.getUsageCount());
                });
    }

    /**
     * 更新问题评分
     */
    public void updateQuestionScore(QuestionDto.QuestionScoreRequest request) {
        questionRepository.findById(request.getQuestionId())
                .filter(Question::getIsActive)
                .ifPresent(question -> {
                    question.updateAverageScore(request.getScore());
                    questionRepository.save(question);
                    
                    log.info("问题评分更新: id={}, score={}", request.getQuestionId(), request.getScore());
                });
    }

    /**
     * 获取问题统计信息
     */
    @Transactional(readOnly = true)
    public QuestionDto.QuestionStats getQuestionStats() {
        // 基础统计
        long totalQuestions = questionRepository.count();
        long activeQuestions = questionRepository.countByIsActiveTrue();
        long todayNewQuestions = questionRepository.countTodayNewQuestions();
        
        // 分类统计
        List<Object[]> categoryData = questionRepository.countQuestionsByCategory();
        List<QuestionDto.CategoryStats> categoryStats = categoryData.stream()
                .map(data -> QuestionDto.CategoryStats.builder()
                        .category((String) data[0])
                        .count((Long) data[1])
                        .percentage(((Long) data[1]).doubleValue() / activeQuestions * 100)
                        .build())
                .collect(Collectors.toList());
        
        // 难度统计
        List<Object[]> difficultyData = questionRepository.countQuestionsByDifficulty();
        List<QuestionDto.DifficultyStats> difficultyStats = difficultyData.stream()
                .map(data -> QuestionDto.DifficultyStats.builder()
                        .difficulty((Question.Difficulty) data[0])
                        .difficultyName(((Question.Difficulty) data[0]).getDescription())
                        .count((Long) data[1])
                        .percentage(((Long) data[1]).doubleValue() / activeQuestions * 100)
                        .build())
                .collect(Collectors.toList());
        
        // 类型统计
        List<Object[]> typeData = questionRepository.countQuestionsByType();
        List<QuestionDto.TypeStats> typeStats = typeData.stream()
                .map(data -> QuestionDto.TypeStats.builder()
                        .type((Question.QuestionType) data[0])
                        .typeName(((Question.QuestionType) data[0]).getDescription())
                        .count((Long) data[1])
                        .percentage(((Long) data[1]).doubleValue() / activeQuestions * 100)
                        .build())
                .collect(Collectors.toList());
        
        // 热门问题
        List<QuestionDto.QuestionInfo> popularQuestions = questionRepository.findTop10ByIsActiveTrueOrderByUsageCountDesc()
                .stream()
                .map(this::convertToQuestionInfo)
                .collect(Collectors.toList());
        
        // 最新问题
        List<QuestionDto.QuestionInfo> recentQuestions = questionRepository.findTop10ByIsActiveTrueOrderByCreatedAtDesc()
                .stream()
                .map(this::convertToQuestionInfo)
                .collect(Collectors.toList());
        
        return QuestionDto.QuestionStats.builder()
                .totalQuestions(totalQuestions)
                .activeQuestions(activeQuestions)
                .todayNewQuestions(todayNewQuestions)
                .categoryStats(categoryStats)
                .difficultyStats(difficultyStats)
                .typeStats(typeStats)
                .popularQuestions(popularQuestions)
                .recentQuestions(recentQuestions)
                .build();
    }

    /**
     * 批量创建问题
     */
    public QuestionDto.BatchOperationResponse batchCreateQuestions(QuestionDto.BatchCreateQuestionRequest request) {
        List<QuestionDto.QuestionInfo> createdQuestions = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        for (QuestionDto.CreateQuestionRequest questionRequest : request.getQuestions()) {
            try {
                QuestionDto.QuestionInfo created = createQuestion(questionRequest);
                createdQuestions.add(created);
                successCount++;
            } catch (Exception e) {
                errors.add("创建问题失败: " + questionRequest.getTitle() + " - " + e.getMessage());
                failureCount++;
                log.error("批量创建问题失败: title={}, error={}", questionRequest.getTitle(), e.getMessage());
            }
        }

        log.info("批量创建问题完成: 成功={}, 失败={}", successCount, failureCount);

        return QuestionDto.BatchOperationResponse.builder()
                .successCount(successCount)
                .failureCount(failureCount)
                .errors(errors)
                .createdQuestions(createdQuestions)
                .build();
    }

    /**
     * 批量删除问题
     */
    public QuestionDto.BatchOperationResponse batchDeleteQuestions(List<Long> questionIds) {
        List<String> errors = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        for (Long questionId : questionIds) {
            try {
                if (deleteQuestion(questionId)) {
                    successCount++;
                } else {
                    errors.add("问题不存在或已删除: ID=" + questionId);
                    failureCount++;
                }
            } catch (Exception e) {
                errors.add("删除问题失败: ID=" + questionId + " - " + e.getMessage());
                failureCount++;
                log.error("批量删除问题失败: id={}, error={}", questionId, e.getMessage());
            }
        }

        log.info("批量删除问题完成: 成功={}, 失败={}", successCount, failureCount);

        return QuestionDto.BatchOperationResponse.builder()
                .successCount(successCount)
                .failureCount(failureCount)
                .errors(errors)
                .build();
    }

    /**
     * 初始化示例问题
     */
    public QuestionDto.BatchOperationResponse initSampleQuestions() {
        List<QuestionDto.CreateQuestionRequest> sampleQuestions = createSampleQuestions();

        // 检查是否已经初始化过
        long existingCount = questionRepository.countByIsActiveTrue();
        if (existingCount > 0) {
            log.info("检测到已存在问题，跳过初始化");
            return QuestionDto.BatchOperationResponse.builder()
                    .successCount(0)
                    .failureCount(0)
                    .errors(Arrays.asList("系统已存在问题，无需重复初始化"))
                    .createdQuestions(new ArrayList<>())
                    .build();
        }

        QuestionDto.BatchCreateQuestionRequest batchRequest = QuestionDto.BatchCreateQuestionRequest.builder()
                .questions(sampleQuestions)
                .build();

        return batchCreateQuestions(batchRequest);
    }

    /**
     * 创建示例问题数据
     */
    private List<QuestionDto.CreateQuestionRequest> createSampleQuestions() {
        List<QuestionDto.CreateQuestionRequest> questions = new ArrayList<>();

        // Java技术问题
        questions.add(QuestionDto.CreateQuestionRequest.builder()
                .title("什么是Java中的多态？")
                .content("请解释Java中多态的概念，并举例说明多态的实现方式和应用场景。")
                .category("Java")
                .difficulty(Question.Difficulty.MEDIUM)
                .type(Question.QuestionType.TECHNICAL)
                .tags(Arrays.asList("面向对象", "多态", "继承"))
                .timeLimit(300)
                .expectedAnswer("多态是面向对象编程的重要特性，指同一个接口可以有多种不同的实现方式...")
                .build());

        questions.add(QuestionDto.CreateQuestionRequest.builder()
                .title("Java中HashMap的实现原理")
                .content("请详细说明HashMap的底层实现原理，包括数据结构、哈希冲突解决方案等。")
                .category("Java")
                .difficulty(Question.Difficulty.HARD)
                .type(Question.QuestionType.TECHNICAL)
                .tags(Arrays.asList("集合框架", "HashMap", "数据结构"))
                .timeLimit(600)
                .build());

        // Python技术问题
        questions.add(QuestionDto.CreateQuestionRequest.builder()
                .title("Python中的装饰器")
                .content("请解释Python装饰器的概念和用法，并写一个简单的装饰器示例。")
                .category("Python")
                .difficulty(Question.Difficulty.MEDIUM)
                .type(Question.QuestionType.TECHNICAL)
                .tags(Arrays.asList("装饰器", "函数式编程"))
                .timeLimit(400)
                .build());

        // 算法问题
        questions.add(QuestionDto.CreateQuestionRequest.builder()
                .title("二分查找算法")
                .content("请实现二分查找算法，并分析其时间复杂度和空间复杂度。")
                .category("算法")
                .difficulty(Question.Difficulty.EASY)
                .type(Question.QuestionType.TECHNICAL)
                .tags(Arrays.asList("查找算法", "二分查找", "时间复杂度"))
                .timeLimit(300)
                .build());

        // 行为问题
        questions.add(QuestionDto.CreateQuestionRequest.builder()
                .title("描述一次解决技术难题的经历")
                .content("请描述一次您在工作或学习中遇到技术难题并成功解决的经历，包括问题背景、解决思路和最终结果。")
                .category("行为面试")
                .difficulty(Question.Difficulty.MEDIUM)
                .type(Question.QuestionType.BEHAVIORAL)
                .tags(Arrays.asList("问题解决", "技术能力", "经验分享"))
                .timeLimit(600)
                .build());

        // 系统设计问题
        questions.add(QuestionDto.CreateQuestionRequest.builder()
                .title("设计一个简单的缓存系统")
                .content("请设计一个简单的缓存系统，考虑缓存策略、数据结构选择、并发处理等方面。")
                .category("系统设计")
                .difficulty(Question.Difficulty.HARD)
                .type(Question.QuestionType.COMPREHENSIVE)
                .tags(Arrays.asList("系统设计", "缓存", "并发"))
                .timeLimit(900)
                .build());

        return questions;
    }

    /**
     * 转换为QuestionInfo DTO
     */
    private QuestionDto.QuestionInfo convertToQuestionInfo(Question question) {
        return QuestionDto.QuestionInfo.builder()
                .id(question.getId())
                .title(question.getTitle())
                .content(question.getContent())
                .category(question.getCategory())
                .difficulty(question.getDifficulty())
                .type(question.getType())
                .tags(question.getTags())
                .expectedAnswer(question.getExpectedAnswer())
                .scoringCriteria(question.getScoringCriteria())
                .timeLimit(question.getTimeLimit())
                .usageCount(question.getUsageCount())
                .averageScore(question.getAverageScore())
                .isActive(question.getIsActive())
                .createdAt(question.getCreatedAt())
                .updatedAt(question.getUpdatedAt())
                .build();
    }
}
