package com.interview.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 问题实体类
 */
@Entity
@Table(name = "questions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class Question {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 200)
    private String title;

    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;

    @Column(length = 50)
    private String category;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private Difficulty difficulty = Difficulty.MEDIUM;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private QuestionType type = QuestionType.TECHNICAL;

    @ElementCollection(fetch = FetchType.LAZY)
    @CollectionTable(
        name = "question_tags",
        joinColumns = @JoinColumn(name = "question_id")
    )
    @Column(name = "tag")
    @Builder.Default
    private List<String> tags = new ArrayList<>();

    @Column(name = "expected_answer", columnDefinition = "TEXT")
    private String expectedAnswer;

    @Column(name = "scoring_criteria", columnDefinition = "TEXT")
    private String scoringCriteria;

    @Column(name = "time_limit")
    private Integer timeLimit; // 建议回答时间(秒)

    @Column(name = "usage_count")
    @Builder.Default
    private Long usageCount = 0L;

    @Column(name = "average_score")
    private Double averageScore;

    @Column(name = "is_active")
    @Builder.Default
    private Boolean isActive = true;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 难度枚举
    public enum Difficulty {
        EASY("简单"),
        MEDIUM("中等"),
        HARD("困难");

        private final String description;

        Difficulty(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 问题类型枚举
    public enum QuestionType {
        TECHNICAL("技术问题"),
        BEHAVIORAL("行为问题"),
        COMPREHENSIVE("综合问题");

        private final String description;

        QuestionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 业务方法
    public void addTag(String tag) {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }
        if (!this.tags.contains(tag)) {
            this.tags.add(tag);
        }
    }

    public void removeTag(String tag) {
        if (this.tags != null) {
            this.tags.remove(tag);
        }
    }

    public boolean hasTag(String tag) {
        return this.tags != null && this.tags.contains(tag);
    }

    public void incrementUsageCount() {
        this.usageCount = (this.usageCount == null ? 0L : this.usageCount) + 1;
    }

    public void updateAverageScore(Double newScore) {
        if (newScore == null) return;
        
        if (this.averageScore == null) {
            this.averageScore = newScore;
        } else {
            // 简单的移动平均算法
            this.averageScore = (this.averageScore + newScore) / 2.0;
        }
    }

    public boolean isEasy() {
        return Difficulty.EASY.equals(this.difficulty);
    }

    public boolean isMedium() {
        return Difficulty.MEDIUM.equals(this.difficulty);
    }

    public boolean isHard() {
        return Difficulty.HARD.equals(this.difficulty);
    }

    public boolean isTechnical() {
        return QuestionType.TECHNICAL.equals(this.type);
    }

    public boolean isBehavioral() {
        return QuestionType.BEHAVIORAL.equals(this.type);
    }

    public boolean isComprehensive() {
        return QuestionType.COMPREHENSIVE.equals(this.type);
    }

    public String getFormattedTimeLimit() {
        if (timeLimit == null || timeLimit <= 0) {
            return "无限制";
        }
        
        int minutes = timeLimit / 60;
        int seconds = timeLimit % 60;
        
        if (minutes > 0) {
            return seconds > 0 ? String.format("%d分%d秒", minutes, seconds) : String.format("%d分钟", minutes);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    public String getFormattedAverageScore() {
        if (averageScore == null) {
            return "暂无评分";
        }
        return String.format("%.1f", averageScore);
    }

    // 创建技术问题的静态方法
    public static Question createTechnicalQuestion(String title, String content, String category, Difficulty difficulty) {
        return Question.builder()
                .title(title)
                .content(content)
                .category(category)
                .difficulty(difficulty)
                .type(QuestionType.TECHNICAL)
                .isActive(true)
                .usageCount(0L)
                .build();
    }

    // 创建行为问题的静态方法
    public static Question createBehavioralQuestion(String title, String content, Difficulty difficulty) {
        return Question.builder()
                .title(title)
                .content(content)
                .category("行为面试")
                .difficulty(difficulty)
                .type(QuestionType.BEHAVIORAL)
                .isActive(true)
                .usageCount(0L)
                .build();
    }

    // 创建综合问题的静态方法
    public static Question createComprehensiveQuestion(String title, String content, String category, Difficulty difficulty) {
        return Question.builder()
                .title(title)
                .content(content)
                .category(category)
                .difficulty(difficulty)
                .type(QuestionType.COMPREHENSIVE)
                .isActive(true)
                .usageCount(0L)
                .build();
    }
}
