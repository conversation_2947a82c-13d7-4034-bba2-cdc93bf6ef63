package com.interview.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 用户实体类
 */
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false, length = 20)
    private String phone;

    @Column(length = 100)
    private String password; // 加密后的密码

    @Column(length = 50)
    private String nickname;

    @Column(length = 20)
    private String experience;

    @Column(length = 50)
    private String position;

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
        name = "user_skills",
        joinColumns = @JoinColumn(name = "user_id")
    )
    @Column(name = "skill")
    @Builder.Default
    private Set<String> skills = new HashSet<>();

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 业务方法
    public void addSkill(String skill) {
        if (this.skills == null) {
            this.skills = new HashSet<>();
        }
        this.skills.add(skill);
    }

    public void removeSkill(String skill) {
        if (this.skills != null) {
            this.skills.remove(skill);
        }
    }

    public boolean hasSkill(String skill) {
        return this.skills != null && this.skills.contains(skill);
    }

    public boolean isProfileComplete() {
        return nickname != null && !nickname.trim().isEmpty() &&
               experience != null && !experience.trim().isEmpty() &&
               position != null && !position.trim().isEmpty() &&
               skills != null && !skills.isEmpty();
    }

    // 格式化手机号显示
    public String getFormattedPhone() {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
}
