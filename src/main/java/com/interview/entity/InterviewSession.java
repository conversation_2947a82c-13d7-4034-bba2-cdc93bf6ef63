package com.interview.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * 面试会话实体类
 */
@Entity
@Table(name = "interview_sessions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class InterviewSession {

    @Id
    private String id; // UUID

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private SessionStatus status = SessionStatus.ACTIVE;

    @Column(name = "start_time", nullable = false)
    @Builder.Default
    private LocalDateTime startTime = LocalDateTime.now();

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @Column(name = "overall_score")
    private Integer overallScore;

    @Column(columnDefinition = "TEXT")
    private String feedback;

    @OneToMany(mappedBy = "session", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Message> messages = new ArrayList<>();

    @ElementCollection(fetch = FetchType.LAZY)
    @CollectionTable(
        name = "session_strengths",
        joinColumns = @JoinColumn(name = "session_id")
    )
    @Column(name = "strength")
    @Builder.Default
    private List<String> strengths = new ArrayList<>();

    @ElementCollection(fetch = FetchType.LAZY)
    @CollectionTable(
        name = "session_improvements",
        joinColumns = @JoinColumn(name = "session_id")
    )
    @Column(name = "improvement")
    @Builder.Default
    private List<String> improvements = new ArrayList<>();

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 会话状态枚举
    public enum SessionStatus {
        ACTIVE("进行中"),
        COMPLETED("已完成"),
        ABANDONED("已放弃");

        private final String description;

        SessionStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 业务方法
    public void complete() {
        this.status = SessionStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
    }

    public void abandon() {
        this.status = SessionStatus.ABANDONED;
        this.endTime = LocalDateTime.now();
    }

    public boolean isActive() {
        return SessionStatus.ACTIVE.equals(this.status);
    }

    public boolean isCompleted() {
        return SessionStatus.COMPLETED.equals(this.status);
    }

    public long getDurationInSeconds() {
        if (startTime == null) {
            return 0;
        }
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return Duration.between(startTime, end).getSeconds();
    }

    public int getMessageCount() {
        return messages != null ? messages.size() : 0;
    }

    public void addMessage(Message message) {
        if (this.messages == null) {
            this.messages = new ArrayList<>();
        }
        message.setSession(this);
        this.messages.add(message);
    }

    public void addStrength(String strength) {
        if (this.strengths == null) {
            this.strengths = new ArrayList<>();
        }
        this.strengths.add(strength);
    }

    public void addImprovement(String improvement) {
        if (this.improvements == null) {
            this.improvements = new ArrayList<>();
        }
        this.improvements.add(improvement);
    }

    // 获取最后一条消息
    public Message getLastMessage() {
        if (messages == null || messages.isEmpty()) {
            return null;
        }
        return messages.get(messages.size() - 1);
    }

    // 获取用户消息数量
    public long getUserMessageCount() {
        if (messages == null) {
            return 0;
        }
        return messages.stream()
                .filter(msg -> Message.MessageRole.USER.equals(msg.getRole()))
                .count();
    }

    // 获取AI消息数量
    public long getAssistantMessageCount() {
        if (messages == null) {
            return 0;
        }
        return messages.stream()
                .filter(msg -> Message.MessageRole.ASSISTANT.equals(msg.getRole()))
                .count();
    }
}
