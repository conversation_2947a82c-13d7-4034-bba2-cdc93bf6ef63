package com.interview.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 消息实体类
 */
@Entity
@Table(name = "messages")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class Message {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "session_id", nullable = false)
    private InterviewSession session;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageRole role;

    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;

    @Column(name = "token_count")
    private Integer tokenCount;

    @Column(name = "response_time")
    private Long responseTime; // 响应时间(毫秒)

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // 消息角色枚举
    public enum MessageRole {
        USER("用户"),
        ASSISTANT("助手");

        private final String description;

        MessageRole(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 业务方法
    public boolean isUserMessage() {
        return MessageRole.USER.equals(this.role);
    }

    public boolean isAssistantMessage() {
        return MessageRole.ASSISTANT.equals(this.role);
    }

    public String getFormattedContent() {
        if (content == null) {
            return "";
        }
        // 可以在这里添加内容格式化逻辑
        return content.trim();
    }

    public int getContentLength() {
        return content != null ? content.length() : 0;
    }

    // 创建用户消息的静态方法
    public static Message createUserMessage(InterviewSession session, String content) {
        return Message.builder()
                .session(session)
                .role(MessageRole.USER)
                .content(content)
                .build();
    }

    // 创建助手消息的静态方法
    public static Message createAssistantMessage(InterviewSession session, String content) {
        return Message.builder()
                .session(session)
                .role(MessageRole.ASSISTANT)
                .content(content)
                .build();
    }

    // 创建带性能指标的助手消息
    public static Message createAssistantMessage(InterviewSession session, String content, 
                                               Integer tokenCount, Long responseTime) {
        return Message.builder()
                .session(session)
                .role(MessageRole.ASSISTANT)
                .content(content)
                .tokenCount(tokenCount)
                .responseTime(responseTime)
                .build();
    }
}
