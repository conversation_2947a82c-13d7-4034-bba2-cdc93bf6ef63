package com.interview.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 用户相关DTO
 */
public class UserDto {

    /**
     * 用户信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {
        private Long id;
        private String phone;
        private String nickname;
        private String experience;
        private String position;
        private Set<String> skills;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
        private boolean profileComplete;
        
        // 格式化手机号显示
        public String getFormattedPhone() {
            if (phone == null || phone.length() != 11) {
                return phone;
            }
            return phone.substring(0, 3) + "****" + phone.substring(7);
        }
    }

    /**
     * 更新用户信息请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateProfileRequest {
        @NotBlank(message = "昵称不能为空")
        @Size(min = 2, max = 20, message = "昵称长度应在2-20个字符之间")
        private String nickname;

        @NotBlank(message = "工作经验不能为空")
        private String experience;

        @NotBlank(message = "职位类型不能为空")
        private String position;

        @NotEmpty(message = "技能标签不能为空")
        @Size(min = 1, max = 10, message = "技能标签数量应在1-10个之间")
        private Set<String> skills;
    }

    /**
     * 用户统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserStats {
        private Long totalInterviews;
        private Long completedInterviews;
        private Double averageScore;
        private Integer bestScore;
        private Long totalMessages;
        private LocalDateTime lastInterviewTime;
        private Long thisMonthInterviews;
    }

    /**
     * 用户简要信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserBrief {
        private Long id;
        private String nickname;
        private String position;
        private String experience;
        private LocalDateTime createdAt;
    }
}
