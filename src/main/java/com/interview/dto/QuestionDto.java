package com.interview.dto;

import com.interview.entity.Question;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 问题相关DTO
 */
public class QuestionDto {

    /**
     * 问题信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuestionInfo {
        private Long id;
        private String title;
        private String content;
        private String category;
        private Question.Difficulty difficulty;
        private Question.QuestionType type;
        private List<String> tags;
        private String expectedAnswer;
        private String scoringCriteria;
        private Integer timeLimit;
        private Long usageCount;
        private Double averageScore;
        private Boolean isActive;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;

        // 格式化方法
        public String getFormattedTimeLimit() {
            if (timeLimit == null || timeLimit <= 0) {
                return "无限制";
            }
            
            int minutes = timeLimit / 60;
            int seconds = timeLimit % 60;
            
            if (minutes > 0) {
                return seconds > 0 ? String.format("%d分%d秒", minutes, seconds) : String.format("%d分钟", minutes);
            } else {
                return String.format("%d秒", seconds);
            }
        }

        public String getFormattedAverageScore() {
            if (averageScore == null) {
                return "暂无评分";
            }
            return String.format("%.1f", averageScore);
        }

        public String getDifficultyDescription() {
            return difficulty != null ? difficulty.getDescription() : "";
        }

        public String getTypeDescription() {
            return type != null ? type.getDescription() : "";
        }
    }

    /**
     * 创建问题请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateQuestionRequest {
        @NotBlank(message = "问题标题不能为空")
        @Size(max = 200, message = "问题标题长度不能超过200个字符")
        private String title;

        @NotBlank(message = "问题内容不能为空")
        private String content;

        @NotBlank(message = "问题分类不能为空")
        @Size(max = 50, message = "分类长度不能超过50个字符")
        private String category;

        @NotNull(message = "难度等级不能为空")
        private Question.Difficulty difficulty;

        @NotNull(message = "问题类型不能为空")
        private Question.QuestionType type;

        private List<String> tags;

        private String expectedAnswer;

        private String scoringCriteria;

        @Min(value = 1, message = "时间限制必须大于0")
        private Integer timeLimit;
    }

    /**
     * 更新问题请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateQuestionRequest {
        @Size(max = 200, message = "问题标题长度不能超过200个字符")
        private String title;

        private String content;

        @Size(max = 50, message = "分类长度不能超过50个字符")
        private String category;

        private Question.Difficulty difficulty;

        private Question.QuestionType type;

        private List<String> tags;

        private String expectedAnswer;

        private String scoringCriteria;

        @Min(value = 1, message = "时间限制必须大于0")
        private Integer timeLimit;

        private Boolean isActive;
    }

    /**
     * 问题查询请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuestionQueryRequest {
        private String category;
        private Question.Difficulty difficulty;
        private Question.QuestionType type;
        private String query; // 搜索关键词
        private List<String> tags;
        private Integer page = 0;
        private Integer size = 10;
        private String sortBy = "createdAt";
        private String sortDirection = "desc";
    }

    /**
     * 问题列表响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuestionListResponse {
        private List<QuestionInfo> questions;
        private Integer totalElements;
        private Integer totalPages;
        private Integer currentPage;
        private Integer pageSize;
        private Boolean hasNext;
        private Boolean hasPrevious;
    }

    /**
     * 问题统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuestionStats {
        private Long totalQuestions;
        private Long activeQuestions;
        private Long todayNewQuestions;
        private List<CategoryStats> categoryStats;
        private List<DifficultyStats> difficultyStats;
        private List<TypeStats> typeStats;
        private List<QuestionInfo> popularQuestions;
        private List<QuestionInfo> recentQuestions;
    }

    /**
     * 分类统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryStats {
        private String category;
        private Long count;
        private Double percentage;
    }

    /**
     * 难度统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifficultyStats {
        private Question.Difficulty difficulty;
        private String difficultyName;
        private Long count;
        private Double percentage;
    }

    /**
     * 类型统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TypeStats {
        private Question.QuestionType type;
        private String typeName;
        private Long count;
        private Double percentage;
    }

    /**
     * 批量创建问题请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchCreateQuestionRequest {
        @NotNull(message = "问题列表不能为空")
        @Size(min = 1, max = 100, message = "批量创建问题数量应在1-100之间")
        private List<CreateQuestionRequest> questions;
    }

    /**
     * 批量操作响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchOperationResponse {
        private Integer successCount;
        private Integer failureCount;
        private List<String> errors;
        private List<QuestionInfo> createdQuestions;
    }

    /**
     * 随机问题请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RandomQuestionRequest {
        private String category;
        private Question.Difficulty difficulty;
        private Question.QuestionType type;
        @Min(value = 1, message = "问题数量必须大于0")
        private Integer count = 1;
        private List<Long> excludeIds; // 排除的问题ID
    }

    /**
     * 问题简要信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuestionBrief {
        private Long id;
        private String title;
        private String category;
        private Question.Difficulty difficulty;
        private Question.QuestionType type;
        private Long usageCount;
        private Double averageScore;
        private LocalDateTime createdAt;
    }

    /**
     * 问题评分请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuestionScoreRequest {
        @NotNull(message = "问题ID不能为空")
        private Long questionId;

        @NotNull(message = "评分不能为空")
        @Min(value = 0, message = "评分不能小于0")
        private Double score;

        private String feedback; // 评分反馈
    }
}
