package com.interview.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 面试相关DTO
 */
public class InterviewDto {

    /**
     * 开始面试请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StartInterviewRequest {
        private String interviewType = "basic"; // 面试类型：basic, technical, behavioral
        private String difficulty = "medium";   // 难度：easy, medium, hard
    }

    /**
     * 开始面试响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StartInterviewResponse {
        private String sessionId;
        private MessageInfo initialMessage;
        private String interviewType;
        private LocalDateTime startTime;
    }

    /**
     * 发送消息请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SendMessageRequest {
        @NotBlank(message = "消息内容不能为空")
        @Size(min = 1, max = 1000, message = "消息长度应在1-1000字符之间")
        private String content;
    }

    /**
     * 发送消息响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SendMessageResponse {
        private MessageInfo message;
        private List<String> suggestions; // 可选的回答建议
        private Integer remainingQuestions; // 剩余问题数
    }

    /**
     * 消息信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MessageInfo {
        private Long id;
        private String sessionId;
        private String role; // user, assistant
        private String content;
        private LocalDateTime timestamp;
        private Integer tokenCount;
        private Long responseTime;
    }

    /**
     * 面试结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterviewResult {
        private String sessionId;
        private Long duration; // 面试时长(秒)
        private Integer messageCount;
        private Integer overallScore; // 总体评分(0-100)
        private List<String> strengths; // 优势
        private List<String> improvements; // 改进建议
        private String feedback; // 详细反馈
        private ScoreBreakdown scoreBreakdown; // 评分详情
        private LocalDateTime completedAt;
    }

    /**
     * 评分详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreBreakdown {
        private Integer technicalScore; // 技术能力评分
        private Integer communicationScore; // 沟通表达评分
        private Integer problemSolvingScore; // 问题解决评分
        private Integer adaptabilityScore; // 适应能力评分
    }

    /**
     * 面试会话信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionInfo {
        private String id;
        private String status; // ACTIVE, COMPLETED, ABANDONED
        private String interviewType;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Long duration;
        private Integer messageCount;
        private Integer overallScore;
    }

    /**
     * 面试历史请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterviewHistoryRequest {
        private Integer page = 0;
        private Integer size = 10;
        private String status; // 可选：筛选状态
        private String sortBy = "createdAt"; // 排序字段
        private String sortDir = "desc"; // 排序方向
    }

    /**
     * 面试历史响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterviewHistoryResponse {
        private List<SessionInfo> sessions;
        private Integer totalElements;
        private Integer totalPages;
        private Integer currentPage;
        private Integer pageSize;
    }

    /**
     * 面试统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterviewStats {
        private Long totalInterviews;
        private Long completedInterviews;
        private Double averageScore;
        private Integer bestScore;
        private Long totalDuration; // 总面试时长(秒)
        private Double averageDuration; // 平均面试时长(秒)
        private Long thisMonthInterviews;
        private List<ScoreTrend> scoreTrends; // 评分趋势
    }

    /**
     * 评分趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreTrend {
        private LocalDateTime date;
        private Integer score;
        private String interviewType;
    }
}
