package com.interview.repository;

import com.interview.entity.Question;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 问题数据访问层
 */
@Repository
public interface QuestionRepository extends JpaRepository<Question, Long> {

    /**
     * 根据分类查找问题
     */
    List<Question> findByCategoryAndIsActiveTrue(String category);

    /**
     * 根据难度查找问题
     */
    List<Question> findByDifficultyAndIsActiveTrue(Question.Difficulty difficulty);

    /**
     * 根据类型查找问题
     */
    List<Question> findByTypeAndIsActiveTrue(Question.QuestionType type);

    /**
     * 根据分类和难度查找问题
     */
    List<Question> findByCategoryAndDifficultyAndIsActiveTrue(String category, Question.Difficulty difficulty);

    /**
     * 根据类型和难度查找问题
     */
    List<Question> findByTypeAndDifficultyAndIsActiveTrue(Question.QuestionType type, Question.Difficulty difficulty);

    /**
     * 根据分类、类型和难度查找问题
     */
    List<Question> findByCategoryAndTypeAndDifficultyAndIsActiveTrue(
            String category, Question.QuestionType type, Question.Difficulty difficulty);

    /**
     * 分页查询活跃问题
     */
    Page<Question> findByIsActiveTrueOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 根据分类分页查询
     */
    Page<Question> findByCategoryAndIsActiveTrueOrderByCreatedAtDesc(String category, Pageable pageable);

    /**
     * 根据难度分页查询
     */
    Page<Question> findByDifficultyAndIsActiveTrueOrderByCreatedAtDesc(Question.Difficulty difficulty, Pageable pageable);

    /**
     * 根据类型分页查询
     */
    Page<Question> findByTypeAndIsActiveTrueOrderByCreatedAtDesc(Question.QuestionType type, Pageable pageable);

    /**
     * 多条件分页查询
     */
    @Query("SELECT q FROM Question q WHERE q.isActive = true " +
           "AND (:category IS NULL OR q.category = :category) " +
           "AND (:difficulty IS NULL OR q.difficulty = :difficulty) " +
           "AND (:type IS NULL OR q.type = :type) " +
           "ORDER BY q.createdAt DESC")
    Page<Question> findByConditions(@Param("category") String category,
                                   @Param("difficulty") Question.Difficulty difficulty,
                                   @Param("type") Question.QuestionType type,
                                   Pageable pageable);

    /**
     * 搜索问题（标题和内容）
     */
    @Query("SELECT q FROM Question q WHERE q.isActive = true AND " +
           "(LOWER(q.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(q.content) LIKE LOWER(CONCAT('%', :query, '%')))")
    Page<Question> searchQuestions(@Param("query") String query, Pageable pageable);

    /**
     * 根据标签查找问题
     */
    @Query("SELECT q FROM Question q JOIN q.tags t WHERE q.isActive = true AND t = :tag")
    List<Question> findByTag(@Param("tag") String tag);

    /**
     * 根据多个标签查找问题
     */
    @Query("SELECT q FROM Question q JOIN q.tags t WHERE q.isActive = true AND t IN :tags " +
           "GROUP BY q HAVING COUNT(t) = :tagCount")
    List<Question> findByTags(@Param("tags") List<String> tags, @Param("tagCount") long tagCount);

    /**
     * 获取所有分类
     */
    @Query("SELECT DISTINCT q.category FROM Question q WHERE q.isActive = true AND q.category IS NOT NULL")
    List<String> findAllCategories();

    /**
     * 获取所有标签
     */
    @Query("SELECT DISTINCT t FROM Question q JOIN q.tags t WHERE q.isActive = true")
    List<String> findAllTags();

    /**
     * 随机获取问题
     */
    @Query(value = "SELECT * FROM questions WHERE is_active = true ORDER BY RANDOM() LIMIT :limit", 
           nativeQuery = true)
    List<Question> findRandomQuestions(@Param("limit") int limit);

    /**
     * 根据条件随机获取问题
     */
    @Query(value = "SELECT * FROM questions WHERE is_active = true " +
           "AND (:category IS NULL OR category = :category) " +
           "AND (:difficulty IS NULL OR difficulty = :difficulty) " +
           "AND (:type IS NULL OR type = :type) " +
           "ORDER BY RANDOM() LIMIT :limit", 
           nativeQuery = true)
    List<Question> findRandomQuestionsByConditions(@Param("category") String category,
                                                  @Param("difficulty") String difficulty,
                                                  @Param("type") String type,
                                                  @Param("limit") int limit);

    /**
     * 获取热门问题（使用次数最多）
     */
    List<Question> findTop10ByIsActiveTrueOrderByUsageCountDesc();

    /**
     * 获取高评分问题
     */
    List<Question> findTop10ByIsActiveTrueAndAverageScoreIsNotNullOrderByAverageScoreDesc();

    /**
     * 获取最新问题
     */
    List<Question> findTop10ByIsActiveTrueOrderByCreatedAtDesc();

    /**
     * 统计问题总数
     */
    long countByIsActiveTrue();

    /**
     * 根据分类统计问题数
     */
    long countByCategoryAndIsActiveTrue(String category);

    /**
     * 根据难度统计问题数
     */
    long countByDifficultyAndIsActiveTrue(Question.Difficulty difficulty);

    /**
     * 根据类型统计问题数
     */
    long countByTypeAndIsActiveTrue(Question.QuestionType type);

    /**
     * 统计各分类的问题数量
     */
    @Query("SELECT q.category, COUNT(q) FROM Question q WHERE q.isActive = true AND q.category IS NOT NULL " +
           "GROUP BY q.category ORDER BY COUNT(q) DESC")
    List<Object[]> countQuestionsByCategory();

    /**
     * 统计各难度的问题数量
     */
    @Query("SELECT q.difficulty, COUNT(q) FROM Question q WHERE q.isActive = true " +
           "GROUP BY q.difficulty ORDER BY COUNT(q) DESC")
    List<Object[]> countQuestionsByDifficulty();

    /**
     * 统计各类型的问题数量
     */
    @Query("SELECT q.type, COUNT(q) FROM Question q WHERE q.isActive = true " +
           "GROUP BY q.type ORDER BY COUNT(q) DESC")
    List<Object[]> countQuestionsByType();

    /**
     * 查找使用次数为0的问题
     */
    List<Question> findByUsageCountAndIsActiveTrue(Long usageCount);

    /**
     * 查找平均分低于阈值的问题
     */
    @Query("SELECT q FROM Question q WHERE q.isActive = true AND q.averageScore < :threshold")
    List<Question> findLowScoreQuestions(@Param("threshold") Double threshold);

    /**
     * 查找平均分高于阈值的问题
     */
    @Query("SELECT q FROM Question q WHERE q.isActive = true AND q.averageScore > :threshold")
    List<Question> findHighScoreQuestions(@Param("threshold") Double threshold);

    /**
     * 根据时间范围查找问题
     */
    List<Question> findByCreatedAtBetweenAndIsActiveTrue(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找今日新增的问题
     */
    @Query("SELECT COUNT(q) FROM Question q WHERE q.isActive = true AND DATE(q.createdAt) = CURRENT_DATE")
    long countTodayNewQuestions();

    /**
     * 软删除问题
     */
    @Query("UPDATE Question q SET q.isActive = false WHERE q.id = :id")
    void softDeleteById(@Param("id") Long id);

    /**
     * 批量软删除问题
     */
    @Query("UPDATE Question q SET q.isActive = false WHERE q.id IN :ids")
    void softDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 恢复已删除的问题
     */
    @Query("UPDATE Question q SET q.isActive = true WHERE q.id = :id")
    void restoreById(@Param("id") Long id);

    /**
     * 查找已删除的问题
     */
    List<Question> findByIsActiveFalseOrderByUpdatedAtDesc();

    /**
     * 更新问题使用次数
     */
    @Query("UPDATE Question q SET q.usageCount = q.usageCount + 1 WHERE q.id = :id")
    void incrementUsageCount(@Param("id") Long id);

    /**
     * 批量更新问题使用次数
     */
    @Query("UPDATE Question q SET q.usageCount = q.usageCount + 1 WHERE q.id IN :ids")
    void incrementUsageCountBatch(@Param("ids") List<Long> ids);
}
