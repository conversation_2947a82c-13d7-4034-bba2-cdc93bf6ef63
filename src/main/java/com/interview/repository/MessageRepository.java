package com.interview.repository;

import com.interview.entity.Message;
import com.interview.entity.InterviewSession;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 消息数据访问层
 */
@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {

    /**
     * 根据会话查找所有消息
     */
    List<Message> findBySessionOrderByCreatedAtAsc(InterviewSession session);

    /**
     * 根据会话ID查找所有消息
     */
    @Query("SELECT m FROM Message m WHERE m.session.id = :sessionId ORDER BY m.createdAt ASC")
    List<Message> findBySessionIdOrderByCreatedAtAsc(@Param("sessionId") String sessionId);

    /**
     * 根据会话和角色查找消息
     */
    List<Message> findBySessionAndRoleOrderByCreatedAtAsc(InterviewSession session, Message.MessageRole role);

    /**
     * 查找会话的最后一条消息
     */
    Optional<Message> findFirstBySessionOrderByCreatedAtDesc(InterviewSession session);

    /**
     * 查找会话的第一条消息
     */
    Optional<Message> findFirstBySessionOrderByCreatedAtAsc(InterviewSession session);

    /**
     * 根据会话ID查找最后一条消息
     */
    @Query("SELECT m FROM Message m WHERE m.session.id = :sessionId ORDER BY m.createdAt DESC")
    Optional<Message> findLastMessageBySessionId(@Param("sessionId") String sessionId);

    /**
     * 统计会话的消息总数
     */
    long countBySession(InterviewSession session);

    /**
     * 统计会话中用户消息数
     */
    long countBySessionAndRole(InterviewSession session, Message.MessageRole role);

    /**
     * 查找会话的最近N条消息
     */
    @Query("SELECT m FROM Message m WHERE m.session = :session ORDER BY m.createdAt DESC")
    List<Message> findRecentMessagesBySession(@Param("session") InterviewSession session);

    /**
     * 查找会话中包含特定关键词的消息
     */
    @Query("SELECT m FROM Message m WHERE m.session = :session AND LOWER(m.content) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<Message> findBySessionAndContentContaining(@Param("session") InterviewSession session, @Param("keyword") String keyword);

    /**
     * 根据时间范围查找消息
     */
    List<Message> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找响应时间超过阈值的消息
     */
    @Query("SELECT m FROM Message m WHERE m.responseTime > :threshold AND m.role = 'ASSISTANT'")
    List<Message> findSlowResponseMessages(@Param("threshold") Long threshold);

    /**
     * 计算会话的平均响应时间
     */
    @Query("SELECT AVG(m.responseTime) FROM Message m WHERE m.session = :session AND m.role = 'ASSISTANT' AND m.responseTime IS NOT NULL")
    Double calculateAverageResponseTime(@Param("session") InterviewSession session);

    /**
     * 统计会话的总Token数
     */
    @Query("SELECT SUM(m.tokenCount) FROM Message m WHERE m.session = :session AND m.tokenCount IS NOT NULL")
    Long calculateTotalTokens(@Param("session") InterviewSession session);

    /**
     * 查找Token数最多的消息
     */
    List<Message> findTop10ByTokenCountIsNotNullOrderByTokenCountDesc();

    /**
     * 统计今日消息总数
     */
    @Query("SELECT COUNT(m) FROM Message m WHERE DATE(m.createdAt) = CURRENT_DATE")
    long countTodayMessages();

    /**
     * 统计各角色的消息数量
     */
    @Query("SELECT m.role, COUNT(m) FROM Message m GROUP BY m.role")
    List<Object[]> countMessagesByRole();

    /**
     * 查找内容最长的消息
     */
    @Query("SELECT m FROM Message m WHERE LENGTH(m.content) = (SELECT MAX(LENGTH(m2.content)) FROM Message m2)")
    List<Message> findLongestMessages();

    /**
     * 查找内容最短的消息
     */
    @Query("SELECT m FROM Message m WHERE LENGTH(m.content) = (SELECT MIN(LENGTH(m2.content)) FROM Message m2)")
    List<Message> findShortestMessages();

    /**
     * 根据会话查找用户消息
     */
    default List<Message> findUserMessagesBySession(InterviewSession session) {
        return findBySessionAndRoleOrderByCreatedAtAsc(session, Message.MessageRole.USER);
    }

    /**
     * 根据会话查找助手消息
     */
    default List<Message> findAssistantMessagesBySession(InterviewSession session) {
        return findBySessionAndRoleOrderByCreatedAtAsc(session, Message.MessageRole.ASSISTANT);
    }

    /**
     * 统计用户消息数
     */
    default long countUserMessagesBySession(InterviewSession session) {
        return countBySessionAndRole(session, Message.MessageRole.USER);
    }

    /**
     * 统计助手消息数
     */
    default long countAssistantMessagesBySession(InterviewSession session) {
        return countBySessionAndRole(session, Message.MessageRole.ASSISTANT);
    }

    /**
     * 删除会话的所有消息
     */
    void deleteBySession(InterviewSession session);

    /**
     * 删除指定时间之前的消息
     */
    @Query("DELETE FROM Message m WHERE m.createdAt < :cutoffTime")
    void deleteMessagesOlderThan(@Param("cutoffTime") LocalDateTime cutoffTime);
}
