package com.interview.repository;

import com.interview.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据手机号查找用户
     */
    Optional<User> findByPhone(String phone);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 根据昵称查找用户（模糊查询）
     */
    List<User> findByNicknameContainingIgnoreCase(String nickname);

    /**
     * 根据职位查找用户
     */
    List<User> findByPosition(String position);

    /**
     * 根据工作经验查找用户
     */
    List<User> findByExperience(String experience);

    /**
     * 查找拥有特定技能的用户
     */
    @Query("SELECT u FROM User u JOIN u.skills s WHERE s = :skill")
    List<User> findBySkill(@Param("skill") String skill);

    /**
     * 查找拥有多个技能的用户
     */
    @Query("SELECT u FROM User u JOIN u.skills s WHERE s IN :skills GROUP BY u HAVING COUNT(s) = :skillCount")
    List<User> findBySkills(@Param("skills") List<String> skills, @Param("skillCount") long skillCount);

    /**
     * 查找个人信息完整的用户
     */
    @Query("SELECT u FROM User u WHERE u.nickname IS NOT NULL AND u.experience IS NOT NULL " +
           "AND u.position IS NOT NULL AND SIZE(u.skills) > 0")
    List<User> findUsersWithCompleteProfile();

    /**
     * 查找个人信息不完整的用户
     */
    @Query("SELECT u FROM User u WHERE u.nickname IS NULL OR u.experience IS NULL " +
           "OR u.position IS NULL OR SIZE(u.skills) = 0")
    List<User> findUsersWithIncompleteProfile();

    /**
     * 根据创建时间范围查找用户
     */
    List<User> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找最近注册的用户
     */
    List<User> findTop10ByOrderByCreatedAtDesc();

    /**
     * 统计用户总数
     */
    @Query("SELECT COUNT(u) FROM User u")
    long countTotalUsers();

    /**
     * 统计今日新增用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE DATE(u.createdAt) = CURRENT_DATE")
    long countTodayNewUsers();

    /**
     * 统计个人信息完整的用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.nickname IS NOT NULL AND u.experience IS NOT NULL " +
           "AND u.position IS NOT NULL AND SIZE(u.skills) > 0")
    long countUsersWithCompleteProfile();

    /**
     * 根据技能统计用户分布
     */
    @Query("SELECT s, COUNT(u) FROM User u JOIN u.skills s GROUP BY s ORDER BY COUNT(u) DESC")
    List<Object[]> countUsersBySkill();

    /**
     * 根据职位统计用户分布
     */
    @Query("SELECT u.position, COUNT(u) FROM User u WHERE u.position IS NOT NULL GROUP BY u.position ORDER BY COUNT(u) DESC")
    List<Object[]> countUsersByPosition();

    /**
     * 根据工作经验统计用户分布
     */
    @Query("SELECT u.experience, COUNT(u) FROM User u WHERE u.experience IS NOT NULL GROUP BY u.experience ORDER BY COUNT(u) DESC")
    List<Object[]> countUsersByExperience();
}
