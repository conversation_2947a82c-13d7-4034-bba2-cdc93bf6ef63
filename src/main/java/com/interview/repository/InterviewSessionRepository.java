package com.interview.repository;

import com.interview.entity.InterviewSession;
import com.interview.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 面试会话数据访问层
 */
@Repository
public interface InterviewSessionRepository extends JpaRepository<InterviewSession, String> {

    /**
     * 根据用户查找所有面试会话
     */
    List<InterviewSession> findByUserOrderByCreatedAtDesc(User user);

    /**
     * 根据用户ID查找所有面试会话
     */
    @Query("SELECT s FROM InterviewSession s WHERE s.user.id = :userId ORDER BY s.createdAt DESC")
    List<InterviewSession> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 根据用户查找活跃的面试会话
     */
    Optional<InterviewSession> findByUserAndStatus(User user, InterviewSession.SessionStatus status);

    /**
     * 根据用户ID查找活跃的面试会话
     */
    @Query("SELECT s FROM InterviewSession s WHERE s.user.id = :userId AND s.status = :status")
    Optional<InterviewSession> findByUserIdAndStatus(@Param("userId") Long userId, 
                                                    @Param("status") InterviewSession.SessionStatus status);

    /**
     * 查找用户的活跃会话
     */
    default Optional<InterviewSession> findActiveSessionByUser(User user) {
        return findByUserAndStatus(user, InterviewSession.SessionStatus.ACTIVE);
    }

    /**
     * 查找用户的活跃会话（通过用户ID）
     */
    default Optional<InterviewSession> findActiveSessionByUserId(Long userId) {
        return findByUserIdAndStatus(userId, InterviewSession.SessionStatus.ACTIVE);
    }

    /**
     * 根据状态查找面试会话
     */
    List<InterviewSession> findByStatusOrderByCreatedAtDesc(InterviewSession.SessionStatus status);

    /**
     * 根据创建时间范围查找面试会话
     */
    List<InterviewSession> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找已完成的面试会话
     */
    List<InterviewSession> findByStatusAndEndTimeIsNotNullOrderByEndTimeDesc(InterviewSession.SessionStatus status);

    /**
     * 查找超时的活跃会话（超过指定时间未活动）
     */
    @Query("SELECT s FROM InterviewSession s WHERE s.status = 'ACTIVE' AND s.updatedAt < :timeoutThreshold")
    List<InterviewSession> findTimeoutActiveSessions(@Param("timeoutThreshold") LocalDateTime timeoutThreshold);

    /**
     * 统计用户的面试会话总数
     */
    long countByUser(User user);

    /**
     * 统计用户的已完成面试会话数
     */
    long countByUserAndStatus(User user, InterviewSession.SessionStatus status);

    /**
     * 统计今日新增的面试会话数
     */
    @Query("SELECT COUNT(s) FROM InterviewSession s WHERE DATE(s.createdAt) = CURRENT_DATE")
    long countTodayNewSessions();

    /**
     * 统计各状态的面试会话数
     */
    @Query("SELECT s.status, COUNT(s) FROM InterviewSession s GROUP BY s.status")
    List<Object[]> countSessionsByStatus();

    /**
     * 查找评分最高的面试会话
     */
    List<InterviewSession> findTop10ByStatusAndOverallScoreIsNotNullOrderByOverallScoreDesc(InterviewSession.SessionStatus status);

    /**
     * 计算用户的平均评分
     */
    @Query("SELECT AVG(s.overallScore) FROM InterviewSession s WHERE s.user = :user AND s.overallScore IS NOT NULL")
    Double calculateAverageScoreByUser(@Param("user") User user);

    /**
     * 查找用户最近的面试会话
     */
    Optional<InterviewSession> findFirstByUserOrderByCreatedAtDesc(User user);

    /**
     * 查找用户最高评分的面试会话
     */
    Optional<InterviewSession> findFirstByUserAndOverallScoreIsNotNullOrderByOverallScoreDesc(User user);

    /**
     * 统计用户本月的面试次数
     */
    @Query("SELECT COUNT(s) FROM InterviewSession s WHERE s.user = :user " +
           "AND YEAR(s.createdAt) = YEAR(CURRENT_DATE) AND MONTH(s.createdAt) = MONTH(CURRENT_DATE)")
    long countUserSessionsThisMonth(@Param("user") User user);

    /**
     * 查找长时间未结束的活跃会话
     */
    @Query("SELECT s FROM InterviewSession s WHERE s.status = 'ACTIVE' " +
           "AND s.startTime < :thresholdTime ORDER BY s.startTime ASC")
    List<InterviewSession> findLongRunningActiveSessions(@Param("thresholdTime") LocalDateTime thresholdTime);

    /**
     * 根据评分范围查找面试会话
     */
    @Query("SELECT s FROM InterviewSession s WHERE s.overallScore BETWEEN :minScore AND :maxScore " +
           "ORDER BY s.overallScore DESC")
    List<InterviewSession> findByScoreRange(@Param("minScore") Integer minScore, @Param("maxScore") Integer maxScore);
}
