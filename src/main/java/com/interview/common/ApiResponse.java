package com.interview.common;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;

/**
 * 统一API响应格式
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {

    /**
     * 响应状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应时间戳
     */
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();

    /**
     * 请求是否成功
     */
    private Boolean success;

    /**
     * 错误详情（仅在失败时返回）
     */
    private String error;

    // 响应状态码常量
    public static final int SUCCESS_CODE = 200;
    public static final int ERROR_CODE = 400;
    public static final int UNAUTHORIZED_CODE = 401;
    public static final int FORBIDDEN_CODE = 403;
    public static final int NOT_FOUND_CODE = 404;
    public static final int INTERNAL_ERROR_CODE = 500;

    /**
     * 成功响应（无数据）
     */
    public static <T> ApiResponse<T> success() {
        return ApiResponse.<T>builder()
                .code(SUCCESS_CODE)
                .message("操作成功")
                .success(true)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 成功响应（带消息）
     */
    public static <T> ApiResponse<T> success(String message) {
        return ApiResponse.<T>builder()
                .code(SUCCESS_CODE)
                .message(message)
                .success(true)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .code(SUCCESS_CODE)
                .message("操作成功")
                .data(data)
                .success(true)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 成功响应（带数据和消息）
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return ApiResponse.<T>builder()
                .code(SUCCESS_CODE)
                .message(message)
                .data(data)
                .success(true)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return ApiResponse.<T>builder()
                .code(ERROR_CODE)
                .message(message)
                .success(false)
                .error(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 失败响应（带状态码）
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .success(false)
                .error(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 失败响应（带详细错误信息）
     */
    public static <T> ApiResponse<T> error(String message, String error) {
        return ApiResponse.<T>builder()
                .code(ERROR_CODE)
                .message(message)
                .success(false)
                .error(error)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 未授权响应
     */
    public static <T> ApiResponse<T> unauthorized(String message) {
        return ApiResponse.<T>builder()
                .code(UNAUTHORIZED_CODE)
                .message(message)
                .success(false)
                .error(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 禁止访问响应
     */
    public static <T> ApiResponse<T> forbidden(String message) {
        return ApiResponse.<T>builder()
                .code(FORBIDDEN_CODE)
                .message(message)
                .success(false)
                .error(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 资源不存在响应
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return ApiResponse.<T>builder()
                .code(NOT_FOUND_CODE)
                .message(message)
                .success(false)
                .error(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 服务器内部错误响应
     */
    public static <T> ApiResponse<T> internalError(String message) {
        return ApiResponse.<T>builder()
                .code(INTERNAL_ERROR_CODE)
                .message(message)
                .success(false)
                .error(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success) && SUCCESS_CODE == code;
    }

    /**
     * 判断响应是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
}
