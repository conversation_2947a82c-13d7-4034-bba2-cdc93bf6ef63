package com.interview.controller;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@RestController
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {})
public class HealthController {

    @GetMapping("/health")
    @CrossOrigin(origins = "*")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "interview-app");
        response.put("version", "0.1.0");
        response.put("status", "UP");
        response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        return response;
    }
}
