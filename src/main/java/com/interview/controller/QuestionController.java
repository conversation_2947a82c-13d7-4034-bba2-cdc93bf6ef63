package com.interview.controller;

import com.interview.common.ApiResponse;
import com.interview.dto.QuestionDto;
import com.interview.entity.Question;
import com.interview.service.QuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 问题管理控制器
 */
@RestController
@RequestMapping("/questions")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "问题管理", description = "问题库管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class QuestionController {

    private final QuestionService questionService;

    /**
     * 获取问题分类
     */
    @GetMapping("/categories")
    @Operation(summary = "获取问题分类", description = "获取所有可用的问题分类列表")
    public ResponseEntity<ApiResponse<List<String>>> getCategories() {
        try {
            List<String> categories = questionService.getAllCategories();
            return ResponseEntity.ok(ApiResponse.success(categories, "获取分类成功"));
        } catch (Exception e) {
            log.error("获取问题分类失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 初始化示例问题
     */
    @PostMapping("/init")
    @Operation(summary = "初始化示例问题", description = "初始化系统预设的示例问题")
    public ResponseEntity<ApiResponse<QuestionDto.BatchOperationResponse>> initSampleQuestions() {
        try {
            QuestionDto.BatchOperationResponse result = questionService.initSampleQuestions();
            return ResponseEntity.ok(ApiResponse.success(result, "示例问题初始化完成"));
        } catch (Exception e) {
            log.error("初始化示例问题失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取问题列表
     */
    @GetMapping
    @Operation(summary = "获取问题列表", description = "分页查询问题列表，支持多种筛选条件")
    public ResponseEntity<ApiResponse<QuestionDto.QuestionListResponse>> getQuestions(
            @Parameter(description = "问题分类") @RequestParam(required = false) String category,
            @Parameter(description = "难度等级") @RequestParam(required = false) String difficulty,
            @Parameter(description = "问题类型") @RequestParam(required = false) String type,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDirection) {
        
        try {
            QuestionDto.QuestionQueryRequest request = QuestionDto.QuestionQueryRequest.builder()
                    .category(category)
                    .difficulty(difficulty != null ? Question.Difficulty.valueOf(difficulty.toUpperCase()) : null)
                    .type(type != null ? Question.QuestionType.valueOf(type.toUpperCase()) : null)
                    .page(page)
                    .size(size)
                    .sortBy(sortBy)
                    .sortDirection(sortDirection)
                    .build();

            QuestionDto.QuestionListResponse result = questionService.getQuestions(request);
            return ResponseEntity.ok(ApiResponse.success(result, "获取问题列表成功"));
            
        } catch (Exception e) {
            log.error("获取问题列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 搜索问题
     */
    @GetMapping("/search")
    @Operation(summary = "搜索问题", description = "根据关键词搜索问题")
    public ResponseEntity<ApiResponse<QuestionDto.QuestionListResponse>> searchQuestions(
            @Parameter(description = "搜索关键词") @RequestParam String query,
            @Parameter(description = "问题分类") @RequestParam(required = false) String category,
            @Parameter(description = "难度等级") @RequestParam(required = false) String difficulty,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        
        try {
            QuestionDto.QuestionQueryRequest request = QuestionDto.QuestionQueryRequest.builder()
                    .query(query)
                    .category(category)
                    .difficulty(difficulty != null ? Question.Difficulty.valueOf(difficulty.toUpperCase()) : null)
                    .page(page)
                    .size(size)
                    .build();

            QuestionDto.QuestionListResponse result = questionService.getQuestions(request);
            return ResponseEntity.ok(ApiResponse.success(result, "搜索问题成功"));
            
        } catch (Exception e) {
            log.error("搜索问题失败: query={}, error={}", query, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取单个问题
     */
    @GetMapping("/{questionId}")
    @Operation(summary = "获取问题详情", description = "根据ID获取问题的详细信息")
    public ResponseEntity<ApiResponse<QuestionDto.QuestionInfo>> getQuestion(
            @Parameter(description = "问题ID") @PathVariable Long questionId,
            Authentication authentication) {
        
        try {
            return questionService.getQuestionById(questionId)
                    .map(question -> ResponseEntity.ok(ApiResponse.success(question, "获取问题详情成功")))
                    .orElse(ResponseEntity.notFound().build());
                    
        } catch (Exception e) {
            log.error("获取问题详情失败: questionId={}, error={}", questionId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 创建问题
     */
    @PostMapping
    @Operation(summary = "创建问题", description = "创建新的面试问题")
    public ResponseEntity<ApiResponse<QuestionDto.QuestionInfo>> createQuestion(
            @Valid @RequestBody QuestionDto.CreateQuestionRequest request,
            Authentication authentication) {
        
        try {
            QuestionDto.QuestionInfo result = questionService.createQuestion(request);
            return ResponseEntity.ok(ApiResponse.success(result, "问题创建成功"));
            
        } catch (Exception e) {
            log.error("创建问题失败: title={}, error={}", request.getTitle(), e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新问题
     */
    @PutMapping("/{questionId}")
    @Operation(summary = "更新问题", description = "更新指定问题的信息")
    public ResponseEntity<ApiResponse<QuestionDto.QuestionInfo>> updateQuestion(
            @Parameter(description = "问题ID") @PathVariable Long questionId,
            @Valid @RequestBody QuestionDto.UpdateQuestionRequest request,
            Authentication authentication) {
        
        try {
            return questionService.updateQuestion(questionId, request)
                    .map(question -> ResponseEntity.ok(ApiResponse.success(question, "问题更新成功")))
                    .orElse(ResponseEntity.notFound().build());
                    
        } catch (Exception e) {
            log.error("更新问题失败: questionId={}, error={}", questionId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除问题
     */
    @DeleteMapping("/{questionId}")
    @Operation(summary = "删除问题", description = "删除指定的问题（软删除）")
    public ResponseEntity<ApiResponse<Void>> deleteQuestion(
            @Parameter(description = "问题ID") @PathVariable Long questionId,
            Authentication authentication) {
        
        try {
            boolean deleted = questionService.deleteQuestion(questionId);
            if (deleted) {
                return ResponseEntity.ok(ApiResponse.success("问题删除成功"));
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("删除问题失败: questionId={}, error={}", questionId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 随机获取问题
     */
    @PostMapping("/random")
    @Operation(summary = "随机获取问题", description = "根据条件随机获取指定数量的问题")
    public ResponseEntity<ApiResponse<List<QuestionDto.QuestionInfo>>> getRandomQuestions(
            @Valid @RequestBody QuestionDto.RandomQuestionRequest request,
            Authentication authentication) {
        
        try {
            List<QuestionDto.QuestionInfo> questions = questionService.getRandomQuestions(request);
            return ResponseEntity.ok(ApiResponse.success(questions, "随机获取问题成功"));
            
        } catch (Exception e) {
            log.error("随机获取问题失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取问题统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取问题统计", description = "获取问题库的统计信息")
    public ResponseEntity<ApiResponse<QuestionDto.QuestionStats>> getQuestionStats(Authentication authentication) {
        try {
            QuestionDto.QuestionStats stats = questionService.getQuestionStats();
            return ResponseEntity.ok(ApiResponse.success(stats, "获取问题统计成功"));
            
        } catch (Exception e) {
            log.error("获取问题统计失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 批量创建问题
     */
    @PostMapping("/bulk")
    @Operation(summary = "批量创建问题", description = "批量创建多个问题")
    public ResponseEntity<ApiResponse<QuestionDto.BatchOperationResponse>> batchCreateQuestions(
            @Valid @RequestBody QuestionDto.BatchCreateQuestionRequest request,
            Authentication authentication) {
        
        try {
            QuestionDto.BatchOperationResponse result = questionService.batchCreateQuestions(request);
            return ResponseEntity.ok(ApiResponse.success(result, "批量创建问题完成"));
            
        } catch (Exception e) {
            log.error("批量创建问题失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 批量删除问题
     */
    @DeleteMapping("/bulk")
    @Operation(summary = "批量删除问题", description = "批量删除多个问题")
    public ResponseEntity<ApiResponse<QuestionDto.BatchOperationResponse>> batchDeleteQuestions(
            @RequestBody List<Long> questionIds,
            Authentication authentication) {
        
        try {
            QuestionDto.BatchOperationResponse result = questionService.batchDeleteQuestions(questionIds);
            return ResponseEntity.ok(ApiResponse.success(result, "批量删除问题完成"));
            
        } catch (Exception e) {
            log.error("批量删除问题失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
}
