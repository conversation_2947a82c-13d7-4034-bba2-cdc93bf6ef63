package com.interview.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@RestController
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {})
public class TestController {

    @Autowired
    private DataSource dataSource;

    @GetMapping("/users/test-db")
    @CrossOrigin(origins = "*")
    public Map<String, Object> testDatabase() {
        Map<String, Object> response = new HashMap<>();
        try {
            Connection connection = dataSource.getConnection();
            response.put("database", "connected");
            response.put("status", "success");
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            connection.close();
        } catch (Exception e) {
            response.put("database", "failed");
            response.put("status", "error");
            response.put("error", e.getMessage());
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
        return response;
    }

    // 用户创建接口已移至UserController，这里删除重复的方法

    // 问题相关的接口已移至QuestionController，这里删除重复的方法

    @GetMapping("/interviews/user/{userId}")
    @CrossOrigin(origins = "*")
    public Map<String, Object> getUserSessions(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("sessions", new Object[0]); // 空数组
        response.put("message", "获取用户面试记录成功");
        return response;
    }
}
