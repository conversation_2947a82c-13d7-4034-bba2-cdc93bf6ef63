package com.interview.controller;

import com.interview.common.ApiResponse;
import com.interview.dto.InterviewDto;
import com.interview.service.InterviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 面试控制器
 */
@RestController
@RequestMapping("/interviews")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "面试管理", description = "面试相关接口")
@SecurityRequirement(name = "bearerAuth")
public class InterviewController {

    private final InterviewService interviewService;

    /**
     * 开始面试
     */
    @PostMapping("/start")
    @Operation(summary = "开始面试", description = "创建新的面试会话并获取开场问题")
    public ResponseEntity<ApiResponse<InterviewDto.StartInterviewResponse>> startInterview(
            @RequestBody(required = false) InterviewDto.StartInterviewRequest request,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            // 使用默认请求参数
            if (request == null) {
                request = InterviewDto.StartInterviewRequest.builder()
                        .interviewType("basic")
                        .difficulty("medium")
                        .build();
            }
            
            InterviewDto.StartInterviewResponse response = interviewService.startInterview(userId, request);
            
            return ResponseEntity.ok(ApiResponse.success(response, "面试开始成功"));
            
        } catch (Exception e) {
            log.error("开始面试失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 发送消息
     */
    @PostMapping("/{sessionId}/message")
    @Operation(summary = "发送消息", description = "向面试会话发送用户消息并获取AI回复")
    public ResponseEntity<ApiResponse<InterviewDto.SendMessageResponse>> sendMessage(
            @PathVariable String sessionId,
            @Valid @RequestBody InterviewDto.SendMessageRequest request,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            InterviewDto.SendMessageResponse response = interviewService.sendMessage(
                    sessionId, request.getContent(), userId);
            
            return ResponseEntity.ok(ApiResponse.success(response, "消息发送成功"));
            
        } catch (Exception e) {
            log.error("发送消息失败: sessionId={}, error={}", sessionId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 结束面试
     */
    @PostMapping("/{sessionId}/end")
    @Operation(summary = "结束面试", description = "结束面试会话并生成评估报告")
    public ResponseEntity<ApiResponse<InterviewDto.InterviewResult>> endInterview(
            @PathVariable String sessionId,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            InterviewDto.InterviewResult result = interviewService.endInterview(sessionId, userId);
            
            return ResponseEntity.ok(ApiResponse.success(result, "面试结束，评估完成"));
            
        } catch (Exception e) {
            log.error("结束面试失败: sessionId={}, error={}", sessionId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 放弃面试
     */
    @PostMapping("/{sessionId}/abandon")
    @Operation(summary = "放弃面试", description = "放弃当前面试会话")
    public ResponseEntity<ApiResponse<Void>> abandonInterview(
            @PathVariable String sessionId,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            interviewService.abandonInterview(sessionId, userId);
            
            return ResponseEntity.ok(ApiResponse.success("面试已放弃"));
            
        } catch (Exception e) {
            log.error("放弃面试失败: sessionId={}, error={}", sessionId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取面试历史
     */
    @GetMapping("/history")
    @Operation(summary = "获取面试历史", description = "获取用户的面试历史记录")
    public ResponseEntity<ApiResponse<InterviewDto.InterviewHistoryResponse>> getInterviewHistory(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            InterviewDto.InterviewHistoryRequest request = InterviewDto.InterviewHistoryRequest.builder()
                    .page(page)
                    .size(size)
                    .status(status)
                    .sortBy(sortBy)
                    .sortDir(sortDir)
                    .build();
            
            InterviewDto.InterviewHistoryResponse response = interviewService.getInterviewHistory(userId, request);
            
            return ResponseEntity.ok(ApiResponse.success(response, "获取面试历史成功"));
            
        } catch (Exception e) {
            log.error("获取面试历史失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取面试统计
     */
    @GetMapping("/stats")
    @Operation(summary = "获取面试统计", description = "获取用户的面试统计信息")
    public ResponseEntity<ApiResponse<InterviewDto.InterviewStats>> getInterviewStats(
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            InterviewDto.InterviewStats stats = interviewService.getInterviewStats(userId);
            
            return ResponseEntity.ok(ApiResponse.success(stats, "获取面试统计成功"));
            
        } catch (Exception e) {
            log.error("获取面试统计失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取会话详情
     */
    @GetMapping("/{sessionId}")
    @Operation(summary = "获取会话详情", description = "获取指定面试会话的详细信息")
    public ResponseEntity<ApiResponse<InterviewDto.SessionInfo>> getSessionInfo(
            @PathVariable String sessionId,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            InterviewDto.SessionInfo sessionInfo = interviewService.getSessionInfo(sessionId, userId);
            
            return ResponseEntity.ok(ApiResponse.success(sessionInfo, "获取会话详情成功"));
            
        } catch (Exception e) {
            log.error("获取会话详情失败: sessionId={}, error={}", sessionId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
}
