package com.interview.controller;

import com.interview.common.ApiResponse;
import com.interview.dto.UserDto;
import com.interview.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "用户管理", description = "用户信息管理相关接口")
public class UserController {

    private final UserService userService;

    /**
     * 用户注册
     */
    @PostMapping
    @Operation(summary = "用户注册", description = "创建新用户账户")
    public ResponseEntity<ApiResponse<UserDto.UserInfo>> createUser(
            @Valid @RequestBody UserDto.CreateUserRequest request) {
        try {
            UserDto.UserInfo user = userService.createUser(request);
            return ResponseEntity.ok(ApiResponse.success(user, "用户创建成功"));

        } catch (Exception e) {
            log.error("用户注册失败: phone={}, error={}", request.getPhone(), e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取当前用户信息 (兼容API文档中的/users/me路径)
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<ApiResponse<UserDto.UserInfo>> getCurrentUser(Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());

            return userService.getUserById(userId)
                    .map(userInfo -> ResponseEntity.ok(ApiResponse.success(userInfo, "获取用户信息成功")))
                    .orElse(ResponseEntity.notFound().build());

        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新指定用户信息 (兼容API文档中的PUT /users/{userId}路径)
     */
    @PutMapping("/{userId}")
    @Operation(summary = "更新用户信息", description = "更新指定用户的个人信息")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<ApiResponse<UserDto.UserInfo>> updateUser(
            @PathVariable Long userId,
            @Valid @RequestBody UserDto.UpdateProfileRequest request,
            Authentication authentication) {
        try {
            // 验证权限：只能更新自己的信息
            Long currentUserId = Long.parseLong(authentication.getName());
            if (!currentUserId.equals(userId)) {
                return ResponseEntity.status(403).body(ApiResponse.error("无权限更新其他用户信息"));
            }

            UserDto.UserInfo updatedUser = userService.updateProfile(userId, request);

            return ResponseEntity.ok(ApiResponse.success(updatedUser, "用户信息更新成功"));

        } catch (Exception e) {
            log.error("更新用户信息失败: userId={}, error={}", userId, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<ApiResponse<UserDto.UserInfo>> getProfile(Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            return userService.getUserById(userId)
                    .map(userInfo -> ResponseEntity.ok(ApiResponse.success(userInfo, "获取用户信息成功")))
                    .orElse(ResponseEntity.notFound().build());
                    
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    @Operation(summary = "更新用户信息", description = "更新当前登录用户的个人信息")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<ApiResponse<UserDto.UserInfo>> updateProfile(
            @Valid @RequestBody UserDto.UpdateProfileRequest request,
            Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            UserDto.UserInfo updatedUser = userService.updateProfile(userId, request);
            
            return ResponseEntity.ok(ApiResponse.success(updatedUser, "用户信息更新成功"));
            
        } catch (Exception e) {
            log.error("更新用户信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取用户统计", description = "获取用户的面试统计信息")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<ApiResponse<UserDto.UserStats>> getUserStats(Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            UserDto.UserStats stats = userService.getUserStats(userId);
            
            return ResponseEntity.ok(ApiResponse.success(stats, "获取用户统计成功"));
            
        } catch (Exception e) {
            log.error("获取用户统计失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 检查用户信息完整性
     */
    @GetMapping("/profile/complete")
    @Operation(summary = "检查信息完整性", description = "检查用户信息是否完整")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<ApiResponse<Boolean>> checkProfileComplete(Authentication authentication) {
        try {
            Long userId = Long.parseLong(authentication.getName());
            
            boolean isComplete = userService.isProfileComplete(userId);
            
            return ResponseEntity.ok(ApiResponse.success(isComplete, "检查完成"));
            
        } catch (Exception e) {
            log.error("检查用户信息完整性失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
}
