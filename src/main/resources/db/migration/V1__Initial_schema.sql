-- 面试模拟应用初始数据库结构
-- Version: 1.0
-- Date: 2025-08-04

-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    phone VARCHAR(20) UNIQUE NOT NULL,
    nickname VARCHAR(50),
    experience VARCHAR(20),
    position VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户技能表
CREATE TABLE user_skills (
    user_id BIGINT NOT NULL,
    skill VARCHAR(50) NOT NULL,
    PRIMARY KEY (user_id, skill),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 面试会话表
CREATE TABLE interview_sessions (
    id VARCHAR(36) PRIMARY KEY, -- UUID
    user_id BIGINT NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    overall_score INTEGER,
    feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 会话优势表
CREATE TABLE session_strengths (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    strength VARCHAR(200) NOT NULL,
    FOREIGN KEY (session_id) REFERENCES interview_sessions(id) ON DELETE CASCADE
);

-- 会话改进建议表
CREATE TABLE session_improvements (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    improvement VARCHAR(200) NOT NULL,
    FOREIGN KEY (session_id) REFERENCES interview_sessions(id) ON DELETE CASCADE
);

-- 消息表
CREATE TABLE messages (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,
    token_count INTEGER,
    response_time BIGINT, -- 响应时间(毫秒)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES interview_sessions(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_created_at ON users(created_at);

CREATE INDEX idx_user_skills_user_id ON user_skills(user_id);
CREATE INDEX idx_user_skills_skill ON user_skills(skill);

CREATE INDEX idx_sessions_user_id ON interview_sessions(user_id);
CREATE INDEX idx_sessions_status ON interview_sessions(status);
CREATE INDEX idx_sessions_created_at ON interview_sessions(created_at);

CREATE INDEX idx_session_strengths_session_id ON session_strengths(session_id);
CREATE INDEX idx_session_improvements_session_id ON session_improvements(session_id);

CREATE INDEX idx_messages_session_id ON messages(session_id);
CREATE INDEX idx_messages_role ON messages(role);
CREATE INDEX idx_messages_created_at ON messages(created_at);

-- 添加约束
ALTER TABLE interview_sessions ADD CONSTRAINT chk_session_status 
    CHECK (status IN ('ACTIVE', 'COMPLETED', 'ABANDONED'));

ALTER TABLE interview_sessions ADD CONSTRAINT chk_overall_score 
    CHECK (overall_score IS NULL OR (overall_score >= 0 AND overall_score <= 100));

-- 添加注释
COMMENT ON TABLE users IS '用户表';
COMMENT ON COLUMN users.id IS '用户ID';
COMMENT ON COLUMN users.phone IS '手机号';
COMMENT ON COLUMN users.nickname IS '昵称';
COMMENT ON COLUMN users.experience IS '工作经验';
COMMENT ON COLUMN users.position IS '职位类型';

COMMENT ON TABLE user_skills IS '用户技能表';
COMMENT ON COLUMN user_skills.user_id IS '用户ID';
COMMENT ON COLUMN user_skills.skill IS '技能名称';

COMMENT ON TABLE interview_sessions IS '面试会话表';
COMMENT ON COLUMN interview_sessions.id IS '会话ID(UUID)';
COMMENT ON COLUMN interview_sessions.user_id IS '用户ID';
COMMENT ON COLUMN interview_sessions.status IS '会话状态';
COMMENT ON COLUMN interview_sessions.start_time IS '开始时间';
COMMENT ON COLUMN interview_sessions.end_time IS '结束时间';
COMMENT ON COLUMN interview_sessions.overall_score IS '总体评分(0-100)';
COMMENT ON COLUMN interview_sessions.feedback IS '反馈内容';

COMMENT ON TABLE session_strengths IS '会话优势表';
COMMENT ON COLUMN session_strengths.session_id IS '会话ID';
COMMENT ON COLUMN session_strengths.strength IS '优势描述';

COMMENT ON TABLE session_improvements IS '会话改进建议表';
COMMENT ON COLUMN session_improvements.session_id IS '会话ID';
COMMENT ON COLUMN session_improvements.improvement IS '改进建议';

COMMENT ON TABLE messages IS '消息表';
COMMENT ON COLUMN messages.id IS '消息ID';
COMMENT ON COLUMN messages.session_id IS '会话ID';
COMMENT ON COLUMN messages.role IS '角色(user/assistant)';
COMMENT ON COLUMN messages.content IS '消息内容';
COMMENT ON COLUMN messages.token_count IS 'Token数量';
COMMENT ON COLUMN messages.response_time IS '响应时间(毫秒)';

-- 插入初始数据(可选)
-- 这里可以插入一些测试数据或默认配置
