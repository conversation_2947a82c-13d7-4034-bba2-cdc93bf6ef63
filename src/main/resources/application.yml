spring:
  application:
    name: interview-app
  
  profiles:
    active: dev
  
  datasource:
    url: **********************************************
    username: ${DB_USERNAME:dev_user}
    password: ${DB_PASSWORD:dev_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: false  # 禁用验证以解决迁移冲突
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  security:
    user:
      name: admin
      password: admin
      roles: ADMIN

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# JWT配置
jwt:
  secret: ${JWT_SECRET:interview_app_secret_key_2024_very_long_secure_key_for_hmac_sha_algorithm_minimum_256_bits_required}
  expiration: 86400 # 24小时
  refresh-expiration: 604800 # 7天

# 大模型配置
llm:
  provider: baidu
  baidu:
    api-key: ${BAIDU_API_KEY:your_baidu_api_key}
    secret-key: ${BAIDU_SECRET_KEY:your_baidu_secret_key}
    model: ERNIE-Bot-turbo
    url: https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions
    timeout: 30000

# 短信配置
sms:
  provider: mock # 开发环境使用mock，生产环境使用实际服务商
  mock:
    enabled: true
    default-code: "123456"

# 应用配置
app:
  cors:
    allowed-origins: 
      - http://localhost:3000
      - http://********:8081
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
  
  rate-limit:
    enabled: true
    requests-per-minute: 60
    
  file:
    upload:
      max-size: 10MB
      allowed-types: jpg,jpeg,png,gif

# 日志配置
logging:
  level:
    com.interview: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/interview-app.log
    max-size: 100MB
    max-history: 30

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true

logging:
  level:
    com.interview: DEBUG
    org.springframework.security: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    show-sql: false
    properties:
      hibernate:
        format_sql: false

logging:
  level:
    com.interview: INFO
    org.springframework.security: WARN
    root: WARN
