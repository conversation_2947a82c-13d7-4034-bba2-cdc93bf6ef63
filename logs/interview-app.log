2025-08-05 08:52:28 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=9h53m25s698ms232?s200ns).
2025-08-05 08:52:47 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /auth/login
2025-08-05 08:52:47 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:52:47 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:52:47 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-08-05 08:52:47 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:52:47 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-05 08:52:47 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-08-05 08:52:47 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0 
    from
        User as generatedAlias0 
    where
        generatedAlias0.phone=:param0 */ select
            user0_.id as id1_7_,
            user0_.created_at as created_2_7_,
            user0_.experience as experien3_7_,
            user0_.nickname as nickname4_7_,
            user0_.password as password5_7_,
            user0_.phone as phone6_7_,
            user0_.position as position7_7_,
            user0_.updated_at as updated_8_7_ 
        from
            users user0_ 
        where
            user0_.phone=?
2025-08-05 08:52:47 [http-nio-8080-exec-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [13900139888]
2025-08-05 08:52:47 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        skills0_.user_id as user_id1_6_0_,
        skills0_.skill as skill2_6_0_ 
    from
        user_skills skills0_ 
    where
        skills0_.user_id=?
2025-08-05 08:52:47 [http-nio-8080-exec-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:52:47 [http-nio-8080-exec-4] INFO  com.interview.service.AuthService - �û������¼�ɹ�: 13900139888
2025-08-05 08:52:47 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:52:48 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /users/me
2025-08-05 08:52:48 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:52:48 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:52:48 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:52:48 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:52:48 [http-nio-8080-exec-7] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:52:48 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:52:48 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:52:48 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:52:48 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:53:22 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:53:22 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:53:22 [http-nio-8080-exec-8] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:53:22 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:53:22 [http-nio-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:53:22 [http-nio-8080-exec-8] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:53:22 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:53:36 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:53:36 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:53:36 [http-nio-8080-exec-9] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:53:36 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:53:36 [http-nio-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:53:36 [http-nio-8080-exec-9] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:53:36 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:53:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:53:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:53:41 [http-nio-8080-exec-10] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:53:41 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:53:41 [http-nio-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:53:41 [http-nio-8080-exec-10] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:53:41 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:53:44 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:53:44 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:53:44 [http-nio-8080-exec-6] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:53:44 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:53:44 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:53:44 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:53:44 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:53:46 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:53:46 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:53:46 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /questions/categories
2025-08-05 08:53:46 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:53:46 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:53:46 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /questions/categories
2025-08-05 08:53:46 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:53:46 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-05 08:53:46 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /questions/categories
2025-08-05 08:53:46 [http-nio-8080-exec-1] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:53:46 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* SELECT
        DISTINCT q.category 
    FROM
        Question q 
    WHERE
        q.isActive = true 
        AND q.category IS NOT NULL */ select
            distinct question0_.category as col_0_0_ 
        from
            questions question0_ 
        where
            question0_.is_active=true 
            and (
                question0_.category is not null
            )
2025-08-05 08:53:46 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:53:46 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:53:46 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:53:46 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:53:46 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:54:12 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:54:12 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:54:12 [http-nio-8080-exec-4] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:54:12 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:54:12 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:54:12 [http-nio-8080-exec-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:54:12 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:54:23 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:54:23 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:54:23 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /questions/categories
2025-08-05 08:54:23 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:54:23 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-05 08:54:23 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /questions/categories
2025-08-05 08:54:23 [http-nio-8080-exec-9] DEBUG org.hibernate.SQL - 
    /* SELECT
        DISTINCT q.category 
    FROM
        Question q 
    WHERE
        q.isActive = true 
        AND q.category IS NOT NULL */ select
            distinct question0_.category as col_0_0_ 
        from
            questions question0_ 
        where
            question0_.is_active=true 
            and (
                question0_.category is not null
            )
2025-08-05 08:54:23 [http-nio-8080-exec-8] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:54:23 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:54:23 [http-nio-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:54:23 [http-nio-8080-exec-8] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:54:23 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:54:23 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:00 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /interviews
2025-08-05 08:55:00 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:00 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:00 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /interviews
2025-08-05 08:55:00 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:00 [http-nio-8080-exec-6] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:55:00 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /interviews
2025-08-05 08:55:00 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:55:00 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:00 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* SELECT
        s 
    FROM
        InterviewSession s 
    WHERE
        s.user.id = :userId 
        AND s.status = :status */ select
            interviews0_.id as id1_0_,
            interviews0_.created_at as created_2_0_,
            interviews0_.end_time as end_time3_0_,
            interviews0_.feedback as feedback4_0_,
            interviews0_.overall_score as overall_5_0_,
            interviews0_.start_time as start_ti6_0_,
            interviews0_.status as status7_0_,
            interviews0_.updated_at as updated_8_0_,
            interviews0_.user_id as user_id9_0_ 
        from
            interview_sessions interviews0_ 
        where
            interviews0_.user_id=? 
            and interviews0_.status=?
2025-08-05 08:55:00 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:00 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [ACTIVE]
2025-08-05 08:55:00 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* load com.interview.entity.InterviewSession */ select
        interviews0_.id as id1_0_1_,
        interviews0_.created_at as created_2_0_1_,
        interviews0_.end_time as end_time3_0_1_,
        interviews0_.feedback as feedback4_0_1_,
        interviews0_.overall_score as overall_5_0_1_,
        interviews0_.start_time as start_ti6_0_1_,
        interviews0_.status as status7_0_1_,
        interviews0_.updated_at as updated_8_0_1_,
        interviews0_.user_id as user_id9_0_1_,
        messages1_.session_id as session_7_1_3_,
        messages1_.id as id1_1_3_,
        messages1_.id as id1_1_0_,
        messages1_.content as content2_1_0_,
        messages1_.created_at as created_3_1_0_,
        messages1_.response_time as response4_1_0_,
        messages1_.role as role5_1_0_,
        messages1_.session_id as session_7_1_0_,
        messages1_.token_count as token_co6_1_0_ 
    from
        interview_sessions interviews0_ 
    left outer join
        messages messages1_ 
            on interviews0_.id=messages1_.session_id 
    where
        interviews0_.id=?
2025-08-05 08:55:00 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [ed860dd7-99b6-4fa7-9552-7f8c9a385312]
2025-08-05 08:55:01 [http-nio-8080-exec-6] ERROR com.interview.service.LLMService - ��ȡ�ٶȷ�������ʧ��: 401 Unauthorized: [no body]
2025-08-05 08:55:01 [http-nio-8080-exec-6] ERROR com.interview.service.LLMService - ���ô�ģ��APIʧ��: ��ȡ��������ʧ��
2025-08-05 08:55:01 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* insert com.interview.entity.InterviewSession
        */ insert 
        into
            interview_sessions
            (created_at, end_time, feedback, overall_score, start_time, status, updated_at, user_id, id) 
        values
            (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [TIMESTAMP] - [2025-08-05T08:55:00.898]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [null]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [null]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [INTEGER] - [null]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [TIMESTAMP] - [2025-08-05T08:55:00.873]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [VARCHAR] - [ACTIVE]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [TIMESTAMP] - [2025-08-05T08:55:00.898]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [BIGINT] - [8]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ed860dd7-99b6-4fa7-9552-7f8c9a385312]
2025-08-05 08:55:01 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    /* insert com.interview.entity.Message
        */ insert 
        into
            messages
            (content, created_at, response_time, role, session_id, token_count) 
        values
            (?, ?, ?, ?, ?, ?)
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [�ܺã��������ϸ˵��һ�¡�]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-08-05T08:55:01.359]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [BIGINT] - [null]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [ASSISTANT]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [ed860dd7-99b6-4fa7-9552-7f8c9a385312]
2025-08-05 08:55:01 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [null]
2025-08-05 08:55:01 [http-nio-8080-exec-6] INFO  c.interview.service.InterviewService - ���Կ�ʼ: userId=8, sessionId=ed860dd7-99b6-4fa7-9552-7f8c9a385312
2025-08-05 08:55:01 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:06 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /interviews
2025-08-05 08:55:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:06 [http-nio-8080-exec-5] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:55:06 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /interviews
2025-08-05 08:55:06 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:55:06 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:06 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    /* SELECT
        s 
    FROM
        InterviewSession s 
    WHERE
        s.user.id = :userId 
        AND s.status = :status */ select
            interviews0_.id as id1_0_,
            interviews0_.created_at as created_2_0_,
            interviews0_.end_time as end_time3_0_,
            interviews0_.feedback as feedback4_0_,
            interviews0_.overall_score as overall_5_0_,
            interviews0_.start_time as start_ti6_0_,
            interviews0_.status as status7_0_,
            interviews0_.updated_at as updated_8_0_,
            interviews0_.user_id as user_id9_0_ 
        from
            interview_sessions interviews0_ 
        where
            interviews0_.user_id=? 
            and interviews0_.status=?
2025-08-05 08:55:06 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:06 [http-nio-8080-exec-5] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [ACTIVE]
2025-08-05 08:55:06 [http-nio-8080-exec-5] ERROR c.i.controller.InterviewController - �������ԻỰʧ��: userId=8, error=������һ�������е����ԣ�������ɻ������ǰ����
2025-08-05 08:55:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:14 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /interviews
2025-08-05 08:55:14 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:14 [http-nio-8080-exec-1] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:55:14 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /interviews
2025-08-05 08:55:14 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:55:14 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:14 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        s 
    FROM
        InterviewSession s 
    WHERE
        s.user.id = :userId 
        AND s.status = :status */ select
            interviews0_.id as id1_0_,
            interviews0_.created_at as created_2_0_,
            interviews0_.end_time as end_time3_0_,
            interviews0_.feedback as feedback4_0_,
            interviews0_.overall_score as overall_5_0_,
            interviews0_.start_time as start_ti6_0_,
            interviews0_.status as status7_0_,
            interviews0_.updated_at as updated_8_0_,
            interviews0_.user_id as user_id9_0_ 
        from
            interview_sessions interviews0_ 
        where
            interviews0_.user_id=? 
            and interviews0_.status=?
2025-08-05 08:55:14 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:14 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [ACTIVE]
2025-08-05 08:55:14 [http-nio-8080-exec-1] ERROR c.i.controller.InterviewController - �������ԻỰʧ��: userId=8, error=������һ�������е����ԣ�������ɻ������ǰ����
2025-08-05 08:55:14 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:23 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /interviews
2025-08-05 08:55:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:23 [http-nio-8080-exec-3] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:55:23 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /interviews
2025-08-05 08:55:23 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:55:23 [http-nio-8080-exec-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:23 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* SELECT
        s 
    FROM
        InterviewSession s 
    WHERE
        s.user.id = :userId 
        AND s.status = :status */ select
            interviews0_.id as id1_0_,
            interviews0_.created_at as created_2_0_,
            interviews0_.end_time as end_time3_0_,
            interviews0_.feedback as feedback4_0_,
            interviews0_.overall_score as overall_5_0_,
            interviews0_.start_time as start_ti6_0_,
            interviews0_.status as status7_0_,
            interviews0_.updated_at as updated_8_0_,
            interviews0_.user_id as user_id9_0_ 
        from
            interview_sessions interviews0_ 
        where
            interviews0_.user_id=? 
            and interviews0_.status=?
2025-08-05 08:55:23 [http-nio-8080-exec-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:23 [http-nio-8080-exec-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [ACTIVE]
2025-08-05 08:55:23 [http-nio-8080-exec-3] ERROR c.i.controller.InterviewController - �������ԻỰʧ��: userId=8, error=������һ�������е����ԣ�������ɻ������ǰ����
2025-08-05 08:55:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:31 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:55:31 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:31 [http-nio-8080-exec-4] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:55:31 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:55:31 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:55:31 [http-nio-8080-exec-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:31 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:32 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 08:55:32 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:32 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /questions/categories
2025-08-05 08:55:32 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:32 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-05 08:55:32 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /questions/categories
2025-08-05 08:55:32 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* SELECT
        DISTINCT q.category 
    FROM
        Question q 
    WHERE
        q.isActive = true 
        AND q.category IS NOT NULL */ select
            distinct question0_.category as col_0_0_ 
        from
            questions question0_ 
        where
            question0_.is_active=true 
            and (
                question0_.category is not null
            )
2025-08-05 08:55:32 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:32 [http-nio-8080-exec-2] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:55:32 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 08:55:32 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:55:32 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:32 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:36 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /interviews
2025-08-05 08:55:36 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:36 [http-nio-8080-exec-9] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:55:36 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured POST /interviews
2025-08-05 08:55:36 [http-nio-8080-exec-9] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:55:36 [http-nio-8080-exec-9] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:36 [http-nio-8080-exec-9] DEBUG org.hibernate.SQL - 
    /* SELECT
        s 
    FROM
        InterviewSession s 
    WHERE
        s.user.id = :userId 
        AND s.status = :status */ select
            interviews0_.id as id1_0_,
            interviews0_.created_at as created_2_0_,
            interviews0_.end_time as end_time3_0_,
            interviews0_.feedback as feedback4_0_,
            interviews0_.overall_score as overall_5_0_,
            interviews0_.start_time as start_ti6_0_,
            interviews0_.status as status7_0_,
            interviews0_.updated_at as updated_8_0_,
            interviews0_.user_id as user_id9_0_ 
        from
            interview_sessions interviews0_ 
        where
            interviews0_.user_id=? 
            and interviews0_.status=?
2025-08-05 08:55:36 [http-nio-8080-exec-9] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:36 [http-nio-8080-exec-9] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [ACTIVE]
2025-08-05 08:55:36 [http-nio-8080-exec-9] ERROR c.i.controller.InterviewController - �������ԻỰʧ��: userId=8, error=������һ�������е����ԣ�������ɻ������ǰ����
2025-08-05 08:55:36 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 08:55:40 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /interviews
2025-08-05 08:55:40 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 08:55:40 [http-nio-8080-exec-8] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 08:55:40 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /interviews
2025-08-05 08:55:40 [http-nio-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 08:55:40 [http-nio-8080-exec-8] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:40 [http-nio-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* SELECT
        s 
    FROM
        InterviewSession s 
    WHERE
        s.user.id = :userId 
        AND s.status = :status */ select
            interviews0_.id as id1_0_,
            interviews0_.created_at as created_2_0_,
            interviews0_.end_time as end_time3_0_,
            interviews0_.feedback as feedback4_0_,
            interviews0_.overall_score as overall_5_0_,
            interviews0_.start_time as start_ti6_0_,
            interviews0_.status as status7_0_,
            interviews0_.updated_at as updated_8_0_,
            interviews0_.user_id as user_id9_0_ 
        from
            interview_sessions interviews0_ 
        where
            interviews0_.user_id=? 
            and interviews0_.status=?
2025-08-05 08:55:40 [http-nio-8080-exec-8] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 08:55:40 [http-nio-8080-exec-8] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [ACTIVE]
2025-08-05 08:55:40 [http-nio-8080-exec-8] ERROR c.i.controller.InterviewController - �������ԻỰʧ��: userId=8, error=������һ�������е����ԣ�������ɻ������ǰ����
2025-08-05 08:55:40 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:04:04 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /users/me
2025-08-05 09:04:04 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:04:04 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:04:04 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 09:04:04 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:04:04 [http-nio-8080-exec-6] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 09:04:04 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 09:04:04 [http-nio-8080-exec-6] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 09:04:04 [http-nio-8080-exec-6] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 09:04:04 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:04:11 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /auth/login
2025-08-05 09:04:11 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:04:11 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:04:11 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-08-05 09:04:11 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:04:11 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-05 09:04:11 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-08-05 09:04:11 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0 
    from
        User as generatedAlias0 
    where
        generatedAlias0.phone=:param0 */ select
            user0_.id as id1_7_,
            user0_.created_at as created_2_7_,
            user0_.experience as experien3_7_,
            user0_.nickname as nickname4_7_,
            user0_.password as password5_7_,
            user0_.phone as phone6_7_,
            user0_.position as position7_7_,
            user0_.updated_at as updated_8_7_ 
        from
            users user0_ 
        where
            user0_.phone=?
2025-08-05 09:04:11 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [13900139888]
2025-08-05 09:04:11 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    select
        skills0_.user_id as user_id1_6_0_,
        skills0_.skill as skill2_6_0_ 
    from
        user_skills skills0_ 
    where
        skills0_.user_id=?
2025-08-05 09:04:11 [http-nio-8080-exec-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 09:04:11 [http-nio-8080-exec-1] INFO  com.interview.service.AuthService - �û������¼�ɹ�: 13900139888
2025-08-05 09:04:11 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:04:30 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /interviews
2025-08-05 09:04:30 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:04:30 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:04:30 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /interviews
2025-08-05 09:04:30 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:04:30 [http-nio-8080-exec-4] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 09:04:30 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /interviews
2025-08-05 09:04:30 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 09:04:30 [http-nio-8080-exec-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 09:04:30 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* SELECT
        s 
    FROM
        InterviewSession s 
    WHERE
        s.user.id = :userId 
        AND s.status = :status */ select
            interviews0_.id as id1_0_,
            interviews0_.created_at as created_2_0_,
            interviews0_.end_time as end_time3_0_,
            interviews0_.feedback as feedback4_0_,
            interviews0_.overall_score as overall_5_0_,
            interviews0_.start_time as start_ti6_0_,
            interviews0_.status as status7_0_,
            interviews0_.updated_at as updated_8_0_,
            interviews0_.user_id as user_id9_0_ 
        from
            interview_sessions interviews0_ 
        where
            interviews0_.user_id=? 
            and interviews0_.status=?
2025-08-05 09:04:30 [http-nio-8080-exec-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 09:04:30 [http-nio-8080-exec-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [ACTIVE]
2025-08-05 09:04:30 [http-nio-8080-exec-4] ERROR c.i.controller.InterviewController - �������ԻỰʧ��: userId=8, error=������һ�������е����ԣ�������ɻ������ǰ����
2025-08-05 09:04:30 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:05:09 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /users/me
2025-08-05 09:05:09 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:05:09 [http-nio-8080-exec-7] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 09:05:09 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /users/me
2025-08-05 09:05:09 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 09:05:09 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 09:05:09 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:05:16 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-08-05 09:05:16 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:05:16 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-05 09:05:16 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-08-05 09:05:16 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    /* select
        generatedAlias0 
    from
        User as generatedAlias0 
    where
        generatedAlias0.phone=:param0 */ select
            user0_.id as id1_7_,
            user0_.created_at as created_2_7_,
            user0_.experience as experien3_7_,
            user0_.nickname as nickname4_7_,
            user0_.password as password5_7_,
            user0_.phone as phone6_7_,
            user0_.position as position7_7_,
            user0_.updated_at as updated_8_7_ 
        from
            users user0_ 
        where
            user0_.phone=?
2025-08-05 09:05:16 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [VARCHAR] - [13900139888]
2025-08-05 09:05:16 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        skills0_.user_id as user_id1_6_0_,
        skills0_.skill as skill2_6_0_ 
    from
        user_skills skills0_ 
    where
        skills0_.user_id=?
2025-08-05 09:05:16 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 09:05:16 [http-nio-8080-exec-2] INFO  com.interview.service.AuthService - �û������¼�ɹ�: 13900139888
2025-08-05 09:05:16 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:05:24 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /interviews/user/8
2025-08-05 09:05:24 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:05:24 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:05:24 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /interviews/user/8
2025-08-05 09:05:24 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:05:24 [http-nio-8080-exec-8] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 09:05:24 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /interviews/user/8
2025-08-05 09:05:24 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-08-05 09:05:35 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing POST /interviews
2025-08-05 09:05:35 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-08-05 09:05:35 [http-nio-8080-exec-10] DEBUG c.i.config.JwtAuthenticationFilter - JWT��֤�ɹ�: userId=8
2025-08-05 09:05:35 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured POST /interviews
2025-08-05 09:05:35 [http-nio-8080-exec-10] DEBUG org.hibernate.SQL - 
    select
        user0_.id as id1_7_0_,
        user0_.created_at as created_2_7_0_,
        user0_.experience as experien3_7_0_,
        user0_.nickname as nickname4_7_0_,
        user0_.password as password5_7_0_,
        user0_.phone as phone6_7_0_,
        user0_.position as position7_7_0_,
        user0_.updated_at as updated_8_7_0_,
        skills1_.user_id as user_id1_6_1_,
        skills1_.skill as skill2_6_1_ 
    from
        users user0_ 
    left outer join
        user_skills skills1_ 
            on user0_.id=skills1_.user_id 
    where
        user0_.id=?
2025-08-05 09:05:35 [http-nio-8080-exec-10] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 09:05:35 [http-nio-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* SELECT
        s 
    FROM
        InterviewSession s 
    WHERE
        s.user.id = :userId 
        AND s.status = :status */ select
            interviews0_.id as id1_0_,
            interviews0_.created_at as created_2_0_,
            interviews0_.end_time as end_time3_0_,
            interviews0_.feedback as feedback4_0_,
            interviews0_.overall_score as overall_5_0_,
            interviews0_.start_time as start_ti6_0_,
            interviews0_.status as status7_0_,
            interviews0_.updated_at as updated_8_0_,
            interviews0_.user_id as user_id9_0_ 
        from
            interview_sessions interviews0_ 
        where
            interviews0_.user_id=? 
            and interviews0_.status=?
2025-08-05 09:05:35 [http-nio-8080-exec-10] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-08-05 09:05:35 [http-nio-8080-exec-10] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [VARCHAR] - [ACTIVE]
2025-08-05 09:05:35 [http-nio-8080-exec-10] ERROR c.i.controller.InterviewController - �������ԻỰʧ��: userId=8, error=������һ�������е����ԣ�������ɻ������ǰ����
2025-08-05 09:05:35 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
